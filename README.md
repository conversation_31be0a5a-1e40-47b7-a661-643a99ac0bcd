# Webinar Platform with Vite Integration

This project uses Vite for efficient JavaScript and CSS bundling with Hot Module Replacement (HMR) and code obfuscation for security.

## Features

- **Hot Module Replacement (HMR)**: See changes instantly without full page reloads
- **JavaScript Obfuscation**: Protects your code from easy reverse engineering
- **Asset Optimization**: Minification and bundling for production
- **Code Splitting**: Automatically optimizes bundle sizes
- **Development Server**: Local development with instant feedback

## Prerequisites

- Node.js (v14 or higher)
- NPM (v7 or higher)
- PHP 8.0+
- Composer

## Setup Instructions

### 1. Installation

```bash
# Install npm dependencies
npm install
```

### 2. Development

To start the Vite development server with HMR:

```bash
npm run dev
```

This will:
- Start the Vite development server
- Enable Hot Module Replacement
- Build assets without obfuscation for easier debugging

### 3. Production Build

To build for production:

```bash
npm run build
```

This will:
- Bundle and optimize all assets
- Apply JavaScript obfuscation for security
- Generate versioned asset files

## Important Files and Directories

- `vite.config.js`: Main Vite configuration with obfuscation settings
- `resources/js/webinar/`: JavaScript source files
- `resources/css/`: CSS source files
- `public/build/`: Compiled output directory (do not edit directly)

## JavaScript Obfuscation

The JavaScript files are protected using obfuscation techniques in production builds:

- Control flow flattening
- Dead code injection
- String array transformations
- Debug protection
- Identifier renaming
- Self-defending code

This makes it difficult for unauthorized users to understand and modify your code.

## Adding New JavaScript/CSS Files

1. Create your file in the `resources/js/webinar/` or `resources/css/` directory
2. Add the file path to the `input` array in `vite.config.js`
3. Import it in your blade file using `@vite('resources/js/webinar/your-file.js')`

## Webinar Template Integration

The webinar blade template (`resources/views/join/webinar.blade.php`) is already set up to use Vite. The template:

1. Loads CSS with `@vite(['resources/css/webinar.css'])`
2. Conditionally loads live webinar scripts
3. Loads common scripts for all webinar types

## Troubleshooting

### Vite server not connecting

Make sure:
- The Vite server is running (`npm run dev`)
- Your Laravel `APP_URL` matches the URL you're using in the browser
- The HMR server host in `vite.config.js` is accessible to your browser

### Assets not updating after changes

Try:
- Clear browser cache
- Restart the Vite development server
- Check browser console for errors

### Production build issues

- Verify you have the proper Node.js and NPM versions
- Run `npm run build` with the `--debug` flag for detailed output
- Check file permissions on the `public/build` directory

## Security Considerations

- Never expose your source files in the public directory
- Keep your `node_modules` outside public access
- The obfuscation offers security through obscurity, but is not foolproof
- Always implement proper server-side validation regardless of client-side code

## License

[Your License Information]


Run host
php artisan serve --host=************


php artisan email:test <EMAIL>
