<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Báo G<PERSON> - G<PERSON><PERSON> 20 Kênh</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .pricing-section {
            padding: 40px;
        }

        .section-title {
            font-size: 1.8rem;
            color: #1f2937;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-color: #4f46e5;
        }

        .feature-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .feature-row:last-child {
            border-bottom: none;
        }

        .feature-label {
            font-weight: 600;
            color: #374151;
            font-size: 1rem;
        }

        .feature-value {
            font-weight: 700;
            color: #4f46e5;
            font-size: 1.1rem;
        }

        .pricing-plans {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .plan-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .plan-card.recommended {
            border-color: #10b981;
            transform: scale(1.05);
        }

        .plan-card.recommended::before {
            content: 'KHUYẾN NGHỊ';
            position: absolute;
            top: 20px;
            right: -30px;
            background: #10b981;
            color: white;
            padding: 5px 40px;
            font-size: 0.8rem;
            font-weight: bold;
            transform: rotate(45deg);
        }

        .plan-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-color: #4f46e5;
        }

        .plan-duration {
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
        }

        .plan-price {
            font-size: 2.5rem;
            font-weight: 800;
            color: #4f46e5;
            margin-bottom: 10px;
        }

        .plan-price .currency {
            font-size: 1.5rem;
            vertical-align: top;
        }

        .plan-setup {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }

        .select-btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .select-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .select-btn.selected {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .addon-section {
            margin-top: 50px;
            background: #f8fafc;
            border-radius: 15px;
            padding: 30px;
        }

        .addon-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .addon-item:hover {
            border-color: #4f46e5;
            transform: translateX(10px);
        }

        .addon-info {
            flex: 1;
        }

        .addon-name {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }

        .addon-details {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .addon-pricing {
            text-align: right;
        }

        .addon-price {
            font-weight: 700;
            color: #4f46e5;
            font-size: 1.1rem;
        }

        .addon-note {
            color: #6b7280;
            font-size: 0.8rem;
        }

        .note {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin-top: 30px;
            border-radius: 0 10px 10px 0;
        }

        .note p {
            color: #1e40af;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .total-section {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 2px solid #0ea5e9;
            color: #0c4a6e;
            padding: 30px;
            margin: 30px 0;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(14, 165, 233, 0.1);
        }

        .total-price {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 10px;
            color: #0ea5e9;
            text-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
        }

        .total-label {
            font-size: 1.2rem;
            opacity: 0.8;
            color: #0c4a6e;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .pricing-plans {
                grid-template-columns: 1fr;
            }

            .plan-card.recommended {
                transform: none;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>Gói Webinar Không Giới Hạn</h1>
        <p>Giải pháp webinar chuyên nghiệp cho doanh nghiệp</p>
    </div>

    <div class="pricing-section">
        <h2 class="section-title">Thông Tin Gói Dịch Vụ</h2>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-row">
                    <span class="feature-label">🎯 Số lượng kênh</span>
                    <span class="feature-value">Không giới hạn</span>
                </div>
                <div class="feature-row">
                    <span class="feature-label">📹 Dung lượng video</span>
                    <span class="feature-value">40GB</span>
                </div>
                <div class="feature-row">
                    <span class="feature-label">⚙️ Dung lượng hệ thống</span>
                    <span class="feature-value">1GB</span>
                </div>
                <div class="feature-row">
                    <span class="feature-label">👥 Data khách hàng</span>
                    <span class="feature-value">Không giới hạn</span>
                </div>
            </div>
        </div>

        <h2 class="section-title">Lựa Chọn Gói Cước</h2>

        <div class="pricing-plans">
            <div class="plan-card">
                <div class="plan-duration">Gói 3 Tháng</div>
                <div class="plan-price">
                    3.300.000<span class="currency">đ</span>
                </div>
                <div class="plan-setup">+ 1.000.000đ phí setup</div>
                <button class="select-btn" onclick="selectPlan(this, 3, 4300000)">Chọn Gói Này</button>
            </div>

            <div class="plan-card">
                <div class="plan-duration">Gói 6 Tháng</div>
                <div class="plan-price">
                    5.500.000<span class="currency">đ</span>
                </div>
                <div class="plan-setup">+ ₫1.000.000 phí setup</div>
                <button class="select-btn" onclick="selectPlan(this, 6, 6500000)">Chọn Gói Này</button>
            </div>

            <div class="plan-card recommended">
                <div class="plan-duration">Gói 12 Tháng</div>
                <div class="plan-price">
                    10.000.000<span class="currency">đ</span>
                </div>
                <div class="plan-setup">Miễn phí thiết lập</div>
                <button class="select-btn" onclick="selectPlan(this, 12, 10000000)">Chọn Gói Này</button>
            </div>
        </div>

        <div class="total-section">
            <div class="total-label" id="selectedPlanInfo">Chưa chọn gói dịch vụ</div>
            <div class="total-price" id="totalPrice">0đ</div>
            <div class="total-label">Tổng Chi Phí Cần Thanh Toán</div>
        </div>

        <div class="addon-section">
            <h3 class="section-title">Mở Rộng Dung Lượng</h3>

            <div class="addon-item" onclick="toggleAddon(this, 500000)">
                <div class="addon-info">
                    <div class="addon-name">📊 Hệ Thống</div>
                    <div class="addon-details">Dung lượng bổ sung: 1GB | Quản lý form, báo cáo, analytics</div>
                </div>
                <div class="addon-pricing">
                    <div class="addon-price">500.000đ</div>
                    <div class="addon-note">/ năm</div>
                </div>
            </div>

            <div class="addon-item" onclick="toggleAddon(this, 500000)">
                <div class="addon-info">
                    <div class="addon-name">🎥 Video</div>
                    <div class="addon-details">Dung lượng bổ sung: 10GB | Thêm dung lượng lưu trữ video</div>
                </div>
                <div class="addon-pricing">
                    <div class="addon-price">₫500.000</div>
                    <div class="addon-note">/ năm</div>
                </div>
            </div>
        </div>

        <div class="note">
            <p><strong>Lưu ý:</strong> Gói dịch vụ bao gồm không giới hạn số kênh webinar, không giới hạn data khách hàng, giao diện phát lai video như livestream, popup form đăng ký, và hệ thống quản lý toàn diện.</p>
        </div>
    </div>
</div>

<script>
    // Initialize with no plan selected
    let selectedPlan = { months: 0, price: 0 };
    let selectedAddons = [];

    function selectPlan(button, months, price) {
        // Remove selected class from all buttons
        document.querySelectorAll('.select-btn').forEach(btn => {
            btn.classList.remove('selected');
            btn.textContent = 'Chọn Gói Này';
        });

        // Add selected class to clicked button
        button.classList.add('selected');
        button.textContent = '✓ Đã Chọn';

        selectedPlan = { months, price };
        updateTotal();
    }

    function toggleAddon(item, price) {
        const isSelected = item.style.backgroundColor === 'rgb(239, 246, 255)';

        if (isSelected) {
            item.style.backgroundColor = '';
            item.style.borderColor = '';
            selectedAddons = selectedAddons.filter(addon => addon !== price);
        } else {
            item.style.backgroundColor = '#eff6ff';
            item.style.borderColor = '#3b82f6';
            selectedAddons.push(price);
        }

        updateTotal();
    }

    function updateTotal() {
        const addonTotal = selectedAddons.reduce((sum, price) => sum + price, 0);
        const total = selectedPlan.price + addonTotal;

        if (selectedPlan.months === 0) {
            document.getElementById('totalPrice').textContent = '0đ';
            document.getElementById('selectedPlanInfo').textContent = 'Chưa chọn gói dịch vụ';
        } else {
            document.getElementById('totalPrice').textContent =
                total.toLocaleString('vi-VN') + 'đ';
            document.getElementById('selectedPlanInfo').textContent =
                `Gói ${selectedPlan.months} tháng đã chọn`;
        }
    }

    // Initialize with no plan selected - user must choose
    document.addEventListener('DOMContentLoaded', function() {
        updateTotal();
    });
</script>
</body>
</html>
