# 📧 LUỒNG HOẠT ĐỘNG HỆ THỐNG EMAIL MARKETING

## 🎯 **TỔNG QUAN**

Hệ thống Email Marketing tự động gửi email đến người đăng ký webinar theo lịch trình được thiết lập trước.

---

## 🔄 **LUỒNG HOẠT ĐỘNG CHI TIẾT**

### **1. THIẾT LẬP CAMPAIGN (Manual)**

```mermaid
graph TD
    A[Admin tạo Campaign] --> B[Chọn Webinar]
    B --> C[Đặt tên & mô tả]
    C --> D[Chọn trạng thái: Draft]
    D --> E[Lưu Campaign]
    E --> F[Campaign được tạo]
```

**Chi tiết:**
- Admin truy cập `/email-marketing/campaigns/create`
- Điền thông tin: Tên, mô tả, chọn webinar
- Trạng thái ban đầu: `Draft` (chưa hoạt động)
- Hệ thống lưu vào bảng `email_campaigns`

---

### **2. TẠO WORKFLOWS (Manual)**

```mermaid
graph TD
    A[Ad<PERSON> và<PERSON>n lý Workflow] --> B[Click 'Thêm Workflow']
    B --> C[Chọn thứ tự: 1, 2, 3...]
    C --> D[Chọn ngày giờ gửi cụ thể]
    D --> E[Viết tiêu đề email]
    E --> F[Viết nội dung email]
    F --> G[Lưu Workflow]
    G --> H[Workflow được tạo]
    H --> I{Có thêm workflow?}
    I -->|Có| B
    I -->|Không| J[Hoàn thành setup]
```

**Chi tiết:**
- Mỗi workflow = 1 email trong chuỗi
- Chọn ngày giờ gửi cụ thể (không phải delay)
- Sử dụng variables: `{name}`, `{webinar_title}`, `{join_url}`
- Lưu vào bảng `email_workflows` và `email_templates`

**Ví dụ Workflows:**
```
Workflow 1: Email chào mừng - Gửi ngày 30/06/2025 lúc 09:00
Workflow 2: Email nhắc nhở - Gửi ngày 01/07/2025 lúc 19:00  
Workflow 3: Email follow-up - Gửi ngày 02/07/2025 lúc 21:00
```

---

### **3. KÍCH HOẠT CAMPAIGN (Manual)**

```mermaid
graph TD
    A[Admin chỉnh sửa Campaign] --> B[Thay đổi status: Draft → Active]
    B --> C[Lưu thay đổi]
    C --> D[Campaign trở thành Active]
    D --> E[Hệ thống bắt đầu quét]
```

**Chi tiết:**
- Admin chuyển campaign từ `Draft` → `Active`
- Chỉ campaigns `Active` mới được xử lý bởi hệ thống
- Có thể tạm dừng bằng cách chuyển về `Paused`

---

### **4. HỆ THỐNG TỰ ĐỘNG XỬ LÝ (Automatic)**

#### **4.1. Scheduled Task (Mỗi phút)**

```mermaid
graph TD
    A[Cron Job chạy mỗi phút] --> B[php artisan email:process-campaigns]
    B --> C[Quét campaigns Active]
    C --> D[Quét participants mới]
    D --> E[Tạo email logs]
    E --> F[Gửi emails đã đến giờ]
```

**Command tự động chạy:**
```bash
# Trong routes/console.php
Schedule::command('email:process-campaigns')->everyMinute();
```

#### **4.2. Xử lý Participants mới**

```mermaid
graph TD
    A[Có người đăng ký webinar mới] --> B[Hệ thống quét campaigns Active]
    B --> C{Campaign có workflows?}
    C -->|Có| D[Tạo email logs cho participant mới]
    C -->|Không| E[Bỏ qua]
    D --> F[Lưu vào bảng email_logs]
    F --> G[Status: pending]
```

**Chi tiết:**
- Khi có participant mới đăng ký webinar
- Hệ thống tự động tạo email logs cho tất cả workflows
- Mỗi email log có `scheduled_at` = thời gian workflow

#### **4.3. Gửi Emails đúng giờ**

```mermaid
graph TD
    A[Hệ thống kiểm tra email_logs] --> B{scheduled_at <= now()?}
    B -->|Có| C[Gửi email]
    B -->|Không| D[Chờ đến giờ]
    C --> E[Cập nhật status: sent]
    E --> F[Ghi log thành công]
    C --> G{Gửi thất bại?}
    G -->|Có| H[Status: failed]
    G -->|Không| E
```

---

### **5. TRACKING & ANALYTICS (Automatic)**

```mermaid
graph TD
    A[Email được gửi] --> B[Status: sent]
    B --> C[User mở email]
    C --> D[Status: opened]
    D --> E[User click link]
    E --> F[Status: clicked]
    
    G[Hệ thống tính toán] --> H[Tỷ lệ mở email]
    G --> I[Tỷ lệ click]
    G --> J[Hiển thị analytics]
```

---

## 📊 **DATABASE FLOW**

### **Tables liên quan:**
```
email_campaigns (Chiến dịch)
├── email_workflows (Luồng email)
│   └── email_templates (Mẫu email)
└── email_logs (Lịch sử gửi)
    └── webinar_participants (Người nhận)
```

### **Data Flow:**
```
1. Campaign tạo → email_campaigns
2. Workflow tạo → email_workflows + email_templates  
3. Participant đăng ký → webinar_participants
4. Hệ thống quét → tạo email_logs
5. Đến giờ gửi → cập nhật email_logs.status
```

---

## ⏰ **TIMELINE VÍ DỤ**

### **Ngày 29/06/2025:**
- ✅ Admin tạo campaign "Khóa học ABC"
- ✅ Tạo 3 workflows với lịch gửi
- ✅ Kích hoạt campaign (Active)

### **Ngày 30/06/2025 - 08:00:**
- 🔄 Có người đăng ký webinar mới
- 🔄 Hệ thống tự động tạo 3 email logs
- 📧 09:00: Gửi email chào mừng

### **Ngày 01/07/2025:**
- 📧 19:00: Gửi email nhắc nhở

### **Ngày 02/07/2025:**
- 📧 21:00: Gửi email follow-up

---

## 🎛️ **CONTROL PANEL**

### **Admin có thể:**
- ✅ Tạo/sửa/xóa campaigns
- ✅ Tạo/sửa/xóa workflows  
- ✅ Thay đổi thời gian gửi
- ✅ Tạm dừng/kích hoạt workflows
- ✅ Xem thống kê realtime
- ✅ Xem logs chi tiết

### **Hệ thống tự động:**
- 🤖 Quét participants mới mỗi phút
- 🤖 Tạo email logs tự động
- 🤖 Gửi emails đúng giờ
- 🤖 Cập nhật trạng thái
- 🤖 Tính toán analytics

---

## 🔧 **TECHNICAL DETAILS**

### **Command Processing:**
```php
// Chạy mỗi phút
php artisan email:process-campaigns

// Test mode (không gửi thật)
php artisan email:process-campaigns --dry-run
```

### **Key Features:**
- ✅ **Scheduled DateTime**: Chọn ngày giờ cụ thể thay vì delay
- ✅ **Auto Scanning**: Tự động quét participants mới
- ✅ **Template Variables**: {name}, {webinar_title}, {join_url}
- ✅ **Status Tracking**: pending → sent → opened → clicked
- ✅ **Error Handling**: failed status với error message
- ✅ **Batch Processing**: Xử lý tối đa 100 emails/lần

### **Security:**
- ✅ **Permission Check**: Chỉ user có quyền mới truy cập
- ✅ **Ownership Validation**: Chỉ sửa được campaigns của mình
- ✅ **CSRF Protection**: Tất cả forms được bảo vệ
- ✅ **Input Validation**: Validate tất cả dữ liệu đầu vào

---

## 🎯 **KẾT QUẢ MONG ĐỢI**

Sau khi thiết lập xong:

1. **Tự động hóa hoàn toàn**: Không cần can thiệp thủ công
2. **Gửi đúng giờ**: Emails gửi chính xác theo lịch
3. **Quét participants mới**: Tự động bao phủ người đăng ký mới
4. **Tracking đầy đủ**: Theo dõi được hiệu quả từng email
5. **Giao diện đẹp**: Dễ quản lý và theo dõi

---

**🚀 Hệ thống hoạt động 24/7 tự động! 🚀**
