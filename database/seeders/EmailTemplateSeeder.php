<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\EmailTemplate;
use App\Models\User;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first admin user
        $adminUser = User::whereHas('roles', function($query) {
            $query->where('name', 'admin');
        })->first();

        if (!$adminUser) {
            $adminUser = User::first();
        }

        if (!$adminUser) {
            $this->command->info('No users found. Please create a user first.');
            return;
        }

        // Welcome email template
        EmailTemplate::create([
            'user_id' => $adminUser->id,
            'name' => 'Email Chào Mừng',
            'subject' => 'Chào mừng {name} đến với {webinar_title}!',
            'content' => '
                <h2>Xin chào {name}!</h2>
                <p><PERSON><PERSON><PERSON> ơn bạn đã đăng ký tham gia webinar <strong>{webinar_title}</strong>.</p>
                <p><strong>Thông tin webinar:</strong></p>
                <ul>
                    <li>Diễn giả: {webinar_speaker}</li>
                    <li>Ngày: {webinar_date}</li>
                    <li>Giờ: {webinar_time}</li>
                </ul>
                <p>Link tham gia: <a href="{join_url}">{join_url}</a></p>
                <p>Mã tham gia: <strong>{join_code}</strong></p>
                <p>Chúng tôi rất mong được gặp bạn!</p>
            ',
            'template_type' => 'welcome',
            'is_default' => true,
        ]);

        // Reminder email template
        EmailTemplate::create([
            'user_id' => $adminUser->id,
            'name' => 'Email Nhắc Nhở',
            'subject' => 'Nhắc nhở: {webinar_title} sắp bắt đầu!',
            'content' => '
                <h2>Xin chào {name}!</h2>
                <p>Đây là lời nhắc nhở về webinar <strong>{webinar_title}</strong> mà bạn đã đăng ký.</p>
                <p><strong>Webinar sẽ bắt đầu trong ít phút nữa!</strong></p>
                <p><strong>Thông tin tham gia:</strong></p>
                <ul>
                    <li>Link tham gia: <a href="{join_url}">{join_url}</a></li>
                    <li>Mã tham gia: <strong>{join_code}</strong></li>
                </ul>
                <p>Hãy chuẩn bị sẵn sàng và tham gia đúng giờ nhé!</p>
            ',
            'template_type' => 'reminder',
            'is_default' => true,
        ]);

        // Follow-up email template
        EmailTemplate::create([
            'user_id' => $adminUser->id,
            'name' => 'Email Theo Dõi',
            'subject' => 'Cảm ơn bạn đã tham gia {webinar_title}',
            'content' => '
                <h2>Xin chào {name}!</h2>
                <p>Cảm ơn bạn đã tham gia webinar <strong>{webinar_title}</strong>.</p>
                <p>Chúng tôi hy vọng bạn đã có những trải nghiệm bổ ích từ buổi webinar.</p>
                <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi.</p>
                <p>Hãy theo dõi để không bỏ lỡ những webinar hấp dẫn tiếp theo!</p>
                <p>Trân trọng,<br>Đội ngũ {webinar_speaker}</p>
            ',
            'template_type' => 'follow_up',
            'is_default' => true,
        ]);

        $this->command->info('Email templates seeded successfully!');
    }
}
