<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Order;
use App\Models\Webinar;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolesAndPermissionsSeeder::class,
            ModuleSeeder::class
            // PaymentSettingsSeeder::class,
//            ModuleSeeder::class,
            // Add the OrderSeeder but comment it by default
            // Uncomment to generate 200k orders for load testing
            // OrderSeeder::class,
        ]);

        // // Seed users
        // \App\Models\User::factory(20)->create();

        // // Seed products
        // \App\Models\Product::factory(20)->create();

        // // Seed webinars
        // \App\Models\Webinar::factory(20)->create();

        // // Seed orders
        // \App\Models\Order::factory(50)->create();
    }
}
