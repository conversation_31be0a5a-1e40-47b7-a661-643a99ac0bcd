<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_workflows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained('email_campaigns')->onDelete('cascade');
            $table->foreignId('email_template_id')->constrained('email_templates')->onDelete('cascade');
            $table->integer('sequence_order')->default(1);
            $table->integer('delay_days')->default(0);
            $table->integer('delay_hours')->default(0);
            $table->integer('delay_minutes')->default(0);
            $table->timestamp('scheduled_at')->nullable();
            $table->json('conditions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['campaign_id', 'sequence_order']);
            $table->index(['email_template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_workflows');
    }
};
