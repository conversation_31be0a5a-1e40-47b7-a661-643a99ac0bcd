<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Webinar;

echo "Testing Virtual Viewer Names Feature\n";
echo "====================================\n\n";

// Get first webinar
$webinar = Webinar::first();

if (!$webinar) {
    echo "❌ No webinar found. Please create a webinar first.\n";
    exit(1);
}

echo "📋 Webinar: {$webinar->title}\n";
echo "👥 Virtual Viewers: {$webinar->virtual_viewers}\n";
echo "🏷️  Virtual Viewer Names: " . ($webinar->virtual_viewer_names ?? 'not set') . "\n\n";

// Test different settings
$testSettings = ['male', 'female', 'both'];

foreach ($testSettings as $setting) {
    echo "Testing setting: {$setting}\n";
    echo "--------------------------------\n";
    
    // Update webinar setting
    $webinar->update(['virtual_viewer_names' => $setting]);
    
    echo "✅ Updated webinar virtual_viewer_names to: {$setting}\n";
    echo "🔄 Webinar setting is now: {$webinar->fresh()->virtual_viewer_names}\n\n";
}

echo "✅ All tests completed successfully!\n";
echo "\n📝 Next steps:\n";
echo "1. Go to webinar create/edit forms to see the new dropdown\n";
echo "2. Create/edit a webinar and select different name options\n";
echo "3. Join the webinar to see the virtual viewer names in action\n";
