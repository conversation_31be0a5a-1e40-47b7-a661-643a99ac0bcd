@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>ử Email Marketing')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-history me-2"></i>L<PERSON>ch Sử Email Marketing
                    </h1>
                    <p class="text-muted mb-0">Theo dõi trạng thái và hiệu suất của các email đã gửi</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" action="{{ route('email-marketing.logs') }}">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">Trạng thái</label>
                                <select name="status" class="form-select">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Chờ gửi</option>
                                    <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>Đã gửi</option>
                                    <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Đã nhận</option>
                                    <option value="opened" {{ request('status') === 'opened' ? 'selected' : '' }}>Đã mở</option>
                                    <option value="clicked" {{ request('status') === 'clicked' ? 'selected' : '' }}>Đã click</option>
                                    <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Thất bại</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Chiến dịch</label>
                                <select name="campaign_id" class="form-select">
                                    <option value="">Tất cả chiến dịch</option>
                                    @foreach($campaigns as $campaign)
                                        <option value="{{ $campaign->id }}" {{ request('campaign_id') == $campaign->id ? 'selected' : '' }}>
                                            {{ $campaign->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Tìm kiếm email</label>
                                <input type="text" name="search" class="form-control" placeholder="Tìm theo email người nhận..." 
                                       value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Lọc
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white text-center">
                    <i class="fas fa-paper-plane fa-2x mb-2"></i>
                    <div class="h4 mb-0">{{ $logs->where('status', 'sent')->count() }}</div>
                    <small>Email Đã Gửi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="card-body text-white text-center">
                    <i class="fas fa-envelope-open fa-2x mb-2"></i>
                    <div class="h4 mb-0">{{ $logs->where('status', 'opened')->count() }}</div>
                    <small>Email Đã Mở</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="card-body text-white text-center">
                    <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                    <div class="h4 mb-0">{{ $logs->where('status', 'clicked')->count() }}</div>
                    <small>Email Đã Click</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body text-white text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div class="h4 mb-0">{{ $logs->where('status', 'failed')->count() }}</div>
                    <small>Email Thất Bại</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-list me-2"></i>Chi Tiết Lịch Sử
                        </h5>
                        <div class="text-muted small">
                            Tổng: {{ $logs->total() }} email
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($logs->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="border-0 px-4 py-3">Người Nhận</th>
                                        <th class="border-0 px-4 py-3">Tiêu Đề</th>
                                        <th class="border-0 px-4 py-3">Chiến Dịch</th>
                                        <th class="border-0 px-4 py-3">Trạng Thái</th>
                                        <th class="border-0 px-4 py-3">Thời Gian</th>
                                        <th class="border-0 px-4 py-3">Tương Tác</th>
                                        <th class="border-0 px-4 py-3">Hành Động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($logs as $log)
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $log->recipient_name ?: 'N/A' }}</div>
                                            <small class="text-muted">{{ $log->recipient_email }}</small>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ Str::limit($log->subject, 40) }}</div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="badge bg-info">{{ $log->campaign->name }}</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($log->status === 'sent')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-paper-plane me-1"></i>Đã gửi
                                                </span>
                                            @elseif($log->status === 'delivered')
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-check me-1"></i>Đã nhận
                                                </span>
                                            @elseif($log->status === 'opened')
                                                <span class="badge bg-info">
                                                    <i class="fas fa-envelope-open me-1"></i>Đã mở
                                                </span>
                                            @elseif($log->status === 'clicked')
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-mouse-pointer me-1"></i>Đã click
                                                </span>
                                            @elseif($log->status === 'failed')
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Thất bại
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-clock me-1"></i>Chờ gửi
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($log->sent_at)
                                                <div class="small">{{ $log->sent_at->format('d/m/Y H:i') }}</div>
                                                <div class="text-muted small">{{ $log->sent_at->diffForHumans() }}</div>
                                            @elseif($log->scheduled_at)
                                                <div class="small text-warning">Lên lịch: {{ $log->scheduled_at->format('d/m/Y H:i') }}</div>
                                            @else
                                                <div class="small text-muted">{{ $log->created_at->format('d/m/Y H:i') }}</div>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="small">
                                                @if($log->open_count > 0)
                                                    <span class="badge bg-light text-dark me-1">
                                                        <i class="fas fa-eye me-1"></i>{{ $log->open_count }}
                                                    </span>
                                                @endif
                                                @if($log->click_count > 0)
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-mouse-pointer me-1"></i>{{ $log->click_count }}
                                                    </span>
                                                @endif
                                                @if($log->open_count == 0 && $log->click_count == 0)
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" 
                                                        onclick="viewEmailContent({{ $log->id }})" title="Xem nội dung">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($log->status === 'failed' && $log->error_message)
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="viewError({{ $log->id }})" title="Xem lỗi">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($logs->hasPages())
                            <div class="card-footer bg-white border-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="text-muted small">
                                        Hiển thị {{ $logs->firstItem() }} - {{ $logs->lastItem() }} 
                                        trong tổng số {{ $logs->total() }} kết quả
                                    </div>
                                    {{ $logs->links() }}
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Không có lịch sử email nào</h5>
                            <p class="text-muted">Chưa có email nào được gửi từ các chiến dịch của bạn</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table tbody tr:hover {
    background-color: #f8f9fa;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75em;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

<script>
function viewEmailContent(logId) {
    // Implementation for viewing email content
    console.log('View email content:', logId);
    // You can implement a modal to show email content
}

function viewError(logId) {
    // Implementation for viewing error details
    console.log('View error:', logId);
    // You can implement a modal to show error details
}
</script>
@endsection
