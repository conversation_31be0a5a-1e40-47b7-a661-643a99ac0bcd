@extends('layouts.app')

@section('title', 'Quản Lý Workflow - ' . $campaign->name)

@section('content')
<style>
/* Loading button animations */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success button animation */
.btn-success .fa-check {
    animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Smooth transitions */
.btn {
    transition: all 0.3s ease;
}

/* TinyMCE Editor Spacing */
.tinymce-editor {
    margin-bottom: 15px;
}

/* Ensure proper spacing between editor and help text */
.form-text {
    margin-top: 10px !important;
}
</style>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-sitemap me-2"></i>Quản Lý Workflow
                    </h1>
                    <p class="text-muted mb-0">
                        Chiến dịch: <strong>{{ $campaign->name }}</strong>
                        @if($campaign->webinar)
                            - Webinar: <strong>{{ $campaign->webinar->title }}</strong>
                        @endif
                    </p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWorkflowModal">
                        <i class="fas fa-plus me-2"></i>Thêm Workflow
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 mb-0 text-primary">{{ $campaign->workflows->count() }}</div>
                                <small class="text-muted">Workflows</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 mb-0 text-success">
                                    @if($campaign->status === 'active')
                                        <i class="fas fa-play-circle"></i> Hoạt động
                                    @elseif($campaign->status === 'paused')
                                        <i class="fas fa-pause-circle"></i> Tạm dừng
                                    @elseif($campaign->status === 'completed')
                                        <i class="fas fa-check-circle"></i> Hoàn thành
                                    @else
                                        <i class="fas fa-edit"></i> Nháp
                                    @endif
                                </div>
                                <small class="text-muted">Trạng thái</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 mb-0 text-info">{{ $campaign->emailLogs->whereIn('status', ['sent', 'opened', 'clicked'])->count() }}</div>
                                <small class="text-muted">Email đã gửi</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                @php
                                    $sent = $campaign->emailLogs->whereIn('status', ['sent', 'opened', 'clicked'])->count();
                                    $opened = $campaign->emailLogs->where('open_count', '>', 0)->count();
                                    $rate = $sent > 0 ? round(($opened / $sent) * 100, 1) : 0;
                                @endphp
                                <div class="h4 mb-0 text-warning">{{ $rate }}%</div>
                                <small class="text-muted">Tỷ lệ mở</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflows List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-list me-2"></i>Danh Sách Workflows
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if($campaign->workflows->count() > 0)
                        <div class="workflow-timeline">
                            @foreach($campaign->workflows->sortBy('sequence_order') as $workflow)
                            <div class="workflow-item">
                                <div class="workflow-step">
                                    <div class="step-number">{{ $workflow->sequence_order }}</div>
                                </div>
                                <div class="workflow-content">
                                    <div class="card border-0 shadow-sm mb-3">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-8">
                                                    <h6 class="mb-2 text-primary">
                                                        {{ $workflow->emailTemplate->subject }}
                                                    </h6>
                                                    <div class="mb-2">
                                                        <span class="badge bg-light text-dark me-2">
                                                            <i class="fas fa-clock me-1"></i>
                                                            @if($workflow->scheduled_at)
                                                                Lên lịch: {{ $workflow->scheduled_at->format('d/m/Y H:i') }}
                                                            @else
                                                                Delay:
                                                                @if($workflow->delay_days > 0)
                                                                    {{ $workflow->delay_days }} ngày
                                                                @endif
                                                                @if($workflow->delay_hours > 0)
                                                                    {{ $workflow->delay_hours }} giờ
                                                                @endif
                                                                @if($workflow->delay_minutes > 0)
                                                                    {{ $workflow->delay_minutes }} phút
                                                                @endif
                                                                @if($workflow->delay_days == 0 && $workflow->delay_hours == 0 && $workflow->delay_minutes == 0)
                                                                    Ngay lập tức
                                                                @endif
                                                            @endif
                                                        </span>
                                                        <span class="badge {{ $workflow->is_active ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $workflow->is_active ? 'Hoạt động' : 'Tạm dừng' }}
                                                        </span>
                                                    </div>
                                                    <div class="text-muted small">
                                                        <i class="fas fa-file-alt me-1"></i>
                                                        {{ Str::limit(strip_tags($workflow->emailTemplate->content), 120) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="row text-center mb-3">
                                                        <div class="col-4">
                                                            <div class="fw-bold text-primary">{{ $workflow->emailLogs->whereIn('status', ['sent', 'opened', 'clicked'])->count() }}</div>
                                                            <small class="text-muted">Đã gửi</small>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="fw-bold text-info">{{ $workflow->emailLogs->where('open_count', '>', 0)->count() }}</div>
                                                            <small class="text-muted">Đã mở</small>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="fw-bold text-success">{{ $workflow->emailLogs->where('click_count', '>', 0)->count() }}</div>
                                                            <small class="text-muted">Đã click</small>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-end">
                                                        <div class="btn-group btn-group-sm">
                                                            <button type="button" class="btn btn-outline-primary"
                                                                    onclick="viewWorkflow({{ $workflow->id }})" title="Xem chi tiết">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-outline-info"
                                                                    onclick="sendTestEmail({{ $workflow->id }})" title="Gửi email thử">
                                                                <i class="fas fa-paper-plane"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-outline-secondary"
                                                                    onclick="editWorkflow({{ $workflow->id }})" title="Chỉnh sửa"
                                                                    data-workflow='@json($workflow->load("emailTemplate"))'>
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-outline-warning"
                                                                    onclick="toggleWorkflow({{ $workflow->id }}, {{ $workflow->is_active ? 'false' : 'true' }})"
                                                                    title="{{ $workflow->is_active ? 'Tạm dừng' : 'Kích hoạt' }}">
                                                                <i class="fas fa-{{ $workflow->is_active ? 'pause' : 'play' }}"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteWorkflow({{ $workflow->id }})" title="Xóa">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có workflow nào</h5>
                            <p class="text-muted">Tạo workflow đầu tiên để bắt đầu gửi email tự động</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWorkflowModal">
                                <i class="fas fa-plus me-2"></i>Thêm Workflow
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Workflow Modal -->
<div class="modal fade" id="addWorkflowModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Thêm Workflow Mới
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createWorkflowForm" action="{{ route('email-marketing.workflows.store', $campaign) }}" method="POST" novalidate>
                @csrf
                <div class="modal-body">
                    <!-- Sequence Order -->
                    <div class="mb-3">
                        <label for="sequence_order" class="form-label fw-bold">
                            Thứ Tự <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control" id="sequence_order" name="sequence_order"
                               value="{{ $campaign->workflows->count() + 1 }}" min="1">
                        <div class="form-text">Thứ tự thực hiện workflow trong chuỗi email</div>
                    </div>

                    <!-- Scheduled Time -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Thời Gian Gửi Email</label>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="scheduled_date" class="form-label">Ngày Gửi <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="scheduled_date" name="scheduled_date"
                                       value="{{ date('Y-m-d') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="scheduled_time" class="form-label">Giờ Gửi <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="scheduled_time" name="scheduled_time"
                                       value="{{ date('H:i') }}">
                            </div>
                        </div>
                        <div class="form-text">Chọn ngày và giờ cụ thể để gửi email này</div>
                    </div>

                    <!-- Email Subject -->
                    <div class="mb-3">
                        <label for="email_subject" class="form-label fw-bold">
                            Tiêu Đề Email <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="email_subject" name="email_subject"
                               placeholder="Nhập tiêu đề email...">
                        <div class="form-text">Có thể sử dụng biến: {name}, {webinar_title}, {join_url}</div>
                    </div>

                    <!-- Email Content -->
                    <div class="mb-3">
                        <label for="email_content" class="form-label fw-bold">
                            Nội Dung Email <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control tinymce-editor" id="email_content" name="email_content" rows="12"
                                  placeholder="Nhập nội dung email..."></textarea>
                        <div class="form-text mt-2">
                            <div class="alert alert-info">
                                <strong><i class="fas fa-info-circle me-2"></i>Biến có thể sử dụng:</strong>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <span class="badge bg-primary me-2">{name}</span>
                                            <span class="text-muted">Tên người đăng ký</span>
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-success me-2">{webinar_title}</span>
                                            <span class="text-muted">Tên webinar</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <span class="badge bg-warning me-2">{join_url}</span>
                                            <span class="text-muted">Link tham gia webinar</span>
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-info me-2">{speaker}</span>
                                            <span class="text-muted">Tên diễn giả</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Lưu Workflow
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Workflow Modal -->
<div class="modal fade" id="editWorkflowModal" tabindex="-1" aria-labelledby="editWorkflowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editWorkflowModalLabel">
                    <i class="fas fa-edit me-2"></i>Chỉnh Sửa Workflow
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editWorkflowForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <!-- Sequence Order -->
                        <div class="col-md-6 mb-3">
                            <label for="edit_sequence_order" class="form-label fw-bold">Thứ Tự <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit_sequence_order" name="sequence_order"
                                   min="1" required>
                            <div class="form-text">Thứ tự thực hiện workflow trong chuỗi email</div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="edit_is_active" class="form-label fw-bold">Trạng Thái</label>
                            <select class="form-select" id="edit_is_active" name="is_active">
                                <option value="1">Hoạt động</option>
                                <option value="0">Tạm dừng</option>
                            </select>
                        </div>
                    </div>

                    <!-- Scheduled Time -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Thời Gian Gửi Email</label>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="edit_scheduled_date" class="form-label">Ngày Gửi <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="edit_scheduled_date" name="scheduled_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_scheduled_time" class="form-label">Giờ Gửi <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="edit_scheduled_time" name="scheduled_time" required>
                            </div>
                        </div>
                        <div class="form-text">Chọn ngày và giờ cụ thể để gửi email này</div>
                    </div>

                    <!-- Email Subject -->
                    <div class="mb-3">
                        <label for="edit_email_subject" class="form-label fw-bold">Tiêu Đề Email <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_email_subject" name="email_subject"
                               placeholder="Nhập tiêu đề email..." required>
                        <div class="form-text">Có thể sử dụng biến: {name}, {webinar_title}, {join_url}</div>
                    </div>

                    <!-- Email Content -->
                    <div class="mb-3">
                        <label for="edit_email_content" class="form-label fw-bold">Nội Dung Email <span class="text-danger">*</span></label>
                        <textarea class="form-control tinymce-editor" id="edit_email_content" name="email_content" rows="12"
                                  placeholder="Nhập nội dung email..." required></textarea>
                        <div class="form-text mt-2">
                            <div class="alert alert-info">
                                <strong><i class="fas fa-info-circle me-2"></i>Biến có thể sử dụng:</strong>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <span class="badge bg-primary me-2">{name}</span>
                                            <span class="text-muted">Tên người đăng ký</span>
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-success me-2">{webinar_title}</span>
                                            <span class="text-muted">Tên webinar</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <span class="badge bg-warning me-2">{join_url}</span>
                                            <span class="text-muted">Link tham gia webinar</span>
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-info me-2">{speaker}</span>
                                            <span class="text-muted">Tên diễn giả</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Hủy
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Lưu Thay Đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Workflow Modal -->
<div class="modal fade" id="viewWorkflowModal" tabindex="-1" aria-labelledby="viewWorkflowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewWorkflowModalLabel">
                    <i class="fas fa-eye me-2"></i>Chi Tiết Workflow
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="viewWorkflowContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1" aria-labelledby="testEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="testEmailModalLabel">
                    <i class="fas fa-paper-plane me-2"></i>Gửi Email Thử
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="testEmailForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Email thử sẽ được gửi với nội dung thực tế của workflow này để bạn kiểm tra.
                    </div>

                    <div class="mb-3">
                        <label for="test_email" class="form-label fw-bold">
                            Email Nhận <span class="text-danger">*</span>
                        </label>
                        <input type="email" class="form-control" id="test_email" name="test_email"
                               placeholder="Nhập địa chỉ email để nhận email thử..." required>
                        <div class="form-text">Email sẽ được gửi đến địa chỉ này để bạn kiểm tra nội dung</div>
                    </div>

                    <div class="mb-3">
                        <label for="test_name" class="form-label fw-bold">Tên Người Nhận (Tùy chọn)</label>
                        <input type="text" class="form-control" id="test_name" name="test_name"
                               placeholder="Tên sẽ được thay thế vào biến {name}">
                        <div class="form-text">Nếu để trống, sẽ sử dụng "Người dùng thử nghiệm"</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Hủy
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane me-2"></i>Gửi Email Thử
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.workflow-timeline {
    position: relative;
    padding: 20px 0;
}

.workflow-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.workflow-item {
    position: relative;
    display: flex;
    align-items-flex-start;
    margin-bottom: 20px;
}

.workflow-step {
    position: relative;
    z-index: 2;
    margin-right: 20px;
}

.step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.workflow-content {
    flex: 1;
    margin-top: 10px;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>

<script>
function viewWorkflow(workflowId) {
    // Fetch and display workflow details
    fetch(`/email-marketing/workflows/${workflowId}`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Thông Tin Cơ Bản</h6>
                        <table class="table table-borderless">
                            <tr><td><strong>Thứ tự:</strong></td><td>${data.sequence_order}</td></tr>
                            <tr><td><strong>Trạng thái:</strong></td><td>
                                <span class="badge ${data.is_active ? 'bg-success' : 'bg-secondary'}">
                                    ${data.is_active ? 'Hoạt động' : 'Tạm dừng'}
                                </span>
                            </td></tr>
                            <tr><td><strong>Thời gian gửi:</strong></td><td>${data.scheduled_at ? new Date(data.scheduled_at).toLocaleString('vi-VN') : 'Chưa lên lịch'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Thống Kê</h6>
                        <table class="table table-borderless">
                            <tr><td><strong>Email đã gửi:</strong></td><td>${data.stats?.total_sent || 0}</td></tr>
                            <tr><td><strong>Email đã mở:</strong></td><td>${data.stats?.total_opened || 0}</td></tr>
                            <tr><td><strong>Email đã click:</strong></td><td>${data.stats?.total_clicked || 0}</td></tr>
                        </table>
                    </div>
                </div>
                <hr>
                <h6 class="text-primary">Nội Dung Email</h6>
                <div class="mb-3">
                    <strong>Tiêu đề:</strong> ${data.email_template?.subject || 'N/A'}
                </div>
                <div class="mb-3">
                    <strong>Nội dung:</strong>
                    <div class="border p-3 bg-light rounded">
                        ${data.email_template?.content || 'N/A'}
                    </div>
                </div>
            `;
            document.getElementById('viewWorkflowContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('viewWorkflowModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi tải thông tin workflow');
        });
}

function editWorkflow(workflowId) {
    // Get workflow data from button attribute
    const button = event.target.closest('button');
    const workflowData = JSON.parse(button.getAttribute('data-workflow'));

    // Populate form fields
    document.getElementById('edit_sequence_order').value = workflowData.sequence_order;
    document.getElementById('edit_is_active').value = workflowData.is_active ? '1' : '0';

    // Handle scheduled_at
    if (workflowData.scheduled_at) {
        const scheduledDate = new Date(workflowData.scheduled_at);
        document.getElementById('edit_scheduled_date').value = scheduledDate.toISOString().split('T')[0];
        document.getElementById('edit_scheduled_time').value = scheduledDate.toTimeString().slice(0, 5);
    }

    document.getElementById('edit_email_subject').value = workflowData.email_template?.subject || '';

    // Store content to set later when TinyMCE is ready
    const content = workflowData.email_template?.content || '';
    document.getElementById('edit_email_content').value = content;

    // Set content after modal is shown and TinyMCE is initialized
    setTimeout(function() {
        if (tinymce.get('edit_email_content')) {
            tinymce.get('edit_email_content').setContent(content);
        }
    }, 300);

    // Set form action
    document.getElementById('editWorkflowForm').action = `/email-marketing/workflows/${workflowId}`;

    // Show modal
    new bootstrap.Modal(document.getElementById('editWorkflowModal')).show();
}

function toggleWorkflow(workflowId, isActive) {
    if (confirm(`Bạn có chắc muốn ${isActive === 'true' ? 'kích hoạt' : 'tạm dừng'} workflow này?`)) {
        fetch(`/email-marketing/workflows/${workflowId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                is_active: isActive === 'true'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra');
        });
    }
}

function deleteWorkflow(workflowId) {
    if (confirm('Bạn có chắc chắn muốn xóa workflow này? Hành động này không thể hoàn tác.')) {
        fetch(`/email-marketing/workflows/${workflowId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra khi xóa workflow');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa workflow');
        });
    }
}

function sendTestEmail(workflowId) {
    // Clear previous form data
    document.getElementById('test_email').value = '';
    document.getElementById('test_name').value = '';

    // Store workflow ID for form submission
    document.getElementById('testEmailForm').setAttribute('data-workflow-id', workflowId);

    // Show modal
    new bootstrap.Modal(document.getElementById('testEmailModal')).show();
}

// Handle edit form submission
document.addEventListener('DOMContentLoaded', function() {
    const editForm = document.getElementById('editWorkflowForm');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get submit button and show loading
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xử lý...';

            // Get content from TinyMCE before submitting
            if (tinymce.get('edit_email_content')) {
                tinymce.get('edit_email_content').save();
            }
            if (tinymce.get('email_content')) {
                tinymce.get('email_content').save();
            }

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Combine date and time
            data.scheduled_datetime = data.scheduled_date + ' ' + data.scheduled_time;

            fetch(this.action, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message briefly
                    submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Thành công!';
                    submitBtn.className = 'btn btn-success';

                    // Close modal and reload page after short delay
                    setTimeout(() => {
                        bootstrap.Modal.getInstance(document.getElementById('editWorkflowModal')).hide();
                        location.reload();
                    }, 1000);
                } else {
                    // Restore button state on error
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                    alert('Có lỗi xảy ra khi cập nhật workflow');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Restore button state on error
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                alert('Có lỗi xảy ra khi cập nhật workflow');
            });
        });
    }

    // Handle create form submission
    const createForm = document.getElementById('createWorkflowForm');
    if (createForm) {
        createForm.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default submission

            // Get submit button and show loading
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang tạo...';

            // Get content from TinyMCE before submitting
            let emailContent = '';
            const editor = tinymce.get('email_content');

            if (editor) {
                console.log('TinyMCE editor found, getting content...');
                emailContent = editor.getContent();
                // Update the textarea value
                document.getElementById('email_content').value = emailContent;
            } else {
                console.log('TinyMCE editor not found, using textarea value...');
                // Fallback to textarea value
                emailContent = document.getElementById('email_content').value;
            }

            console.log('Email content:', emailContent);

            // Custom validation
            const sequenceOrder = document.getElementById('sequence_order').value;
            const scheduledDate = document.getElementById('scheduled_date').value;
            const scheduledTime = document.getElementById('scheduled_time').value;
            const emailSubject = document.getElementById('email_subject').value;

            if (!sequenceOrder || sequenceOrder < 1) {
                alert('Vui lòng nhập thứ tự workflow hợp lệ');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            if (!scheduledDate) {
                alert('Vui lòng chọn ngày gửi email');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            if (!scheduledTime) {
                alert('Vui lòng chọn giờ gửi email');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            if (!emailSubject.trim()) {
                alert('Vui lòng nhập tiêu đề email');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            if (!emailContent.trim()) {
                alert('Vui lòng nhập nội dung email');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            // Create FormData and submit via fetch
            const formData = new FormData(createForm);

            fetch(createForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (response.ok) {
                        // Show success message briefly
                        submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Tạo thành công!';
                        submitBtn.className = 'btn btn-success';

                        // Reload page after short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        // Try to get error message from response
                        response.text().then(text => {
                            console.error('Server error:', text);
                            // Restore button state on error
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalText;
                            submitBtn.className = 'btn btn-primary';
                            alert('Có lỗi xảy ra khi tạo workflow. Vui lòng kiểm tra console để biết chi tiết.');
                        });
                    }
                })
                .catch(error => {
                    console.error('Network error:', error);
                    // Restore button state on error
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                    submitBtn.className = 'btn btn-primary';
                    alert('Có lỗi mạng xảy ra. Vui lòng thử lại.');
                });
        });
    }

    // Handle test email form submission
    const testEmailForm = document.getElementById('testEmailForm');
    if (testEmailForm) {
        testEmailForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const workflowId = this.getAttribute('data-workflow-id');
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang gửi...';
            submitBtn.disabled = true;

            fetch(`/email-marketing/workflows/${workflowId}/test-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('testEmailModal')).hide();
                    alert('Email thử đã được gửi thành công!');
                } else {
                    alert(data.message || 'Có lỗi xảy ra khi gửi email thử');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi gửi email thử');
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
});



// Initialize TinyMCE for rich text editing (Free Version)
function initializeTinyMCE() {
    tinymce.init({
        selector: '.tinymce-editor',
        height: 400,
        menubar: 'file edit view insert format tools table help',
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | bold italic underline strikethrough | ' +
                'alignleft aligncenter alignright alignjustify | ' +
                'bullist numlist outdent indent | removeformat | ' +
                'forecolor backcolor | link image | variables | ' +
                'table | preview code fullscreen help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.4; }',
        skin: 'oxide',
        content_css: 'default',
        branding: false,
        setup: function (editor) {
            // Add custom button for inserting variables
            editor.ui.registry.addSplitButton('variables', {
                text: 'Biến',
                icon: 'template',
                onAction: function () {
                    // Default action - show menu
                },
                onItemAction: function (api, value) {
                    editor.insertContent(value);
                },
                fetch: function (callback) {
                    var items = [
                        {
                            type: 'choiceitem',
                            text: 'Tên người đăng ký',
                            value: '{name}'
                        },
                        {
                            type: 'choiceitem',
                            text: 'Tên webinar',
                            value: '{webinar_title}'
                        },
                        {
                            type: 'choiceitem',
                            text: 'Link tham gia',
                            value: '{join_url}'
                        },
                        {
                            type: 'choiceitem',
                            text: 'Tên diễn giả',
                            value: '{speaker}'
                        }
                    ];
                    callback(items);
                }
            });
        },
        toolbar: 'undo redo | blocks | bold italic underline strikethrough | ' +
                'alignleft aligncenter alignright alignjustify | ' +
                'bullist numlist outdent indent | removeformat | ' +
                'forecolor backcolor | link image | variables | ' +
                'preview code fullscreen help',
        block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre'
    });
}

// Initialize TinyMCE when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE when create modal is shown
    document.getElementById('addWorkflowModal').addEventListener('shown.bs.modal', function () {
        setTimeout(function() {
            // Remove existing instance first
            if (tinymce.get('email_content')) {
                tinymce.get('email_content').remove();
            }
            // Initialize new instance
            tinymce.init({
                selector: '#email_content',
                height: 400,
                menubar: 'file edit view insert format tools table help',
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount'
                ],
                toolbar: 'undo redo | blocks | bold italic underline strikethrough | ' +
                        'alignleft aligncenter alignright alignjustify | ' +
                        'bullist numlist outdent indent | removeformat | ' +
                        'forecolor backcolor | link image | variables | ' +
                        'table | preview code fullscreen help',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.4; }',
                skin: 'oxide',
                content_css: 'default',
                branding: false,
                setup: function (editor) {
                    // Add custom button for inserting variables
                    editor.ui.registry.addSplitButton('variables', {
                        text: 'Biến',
                        icon: 'template',
                        onAction: function () {
                            // Default action - show menu
                        },
                        onItemAction: function (api, value) {
                            editor.insertContent(value);
                        },
                        fetch: function (callback) {
                            var items = [
                                {
                                    type: 'choiceitem',
                                    text: 'Tên người đăng ký',
                                    value: '{name}'
                                },
                                {
                                    type: 'choiceitem',
                                    text: 'Tên webinar',
                                    value: '{webinar_title}'
                                },
                                {
                                    type: 'choiceitem',
                                    text: 'Link tham gia',
                                    value: '{join_url}'
                                },
                                {
                                    type: 'choiceitem',
                                    text: 'Tên diễn giả',
                                    value: '{speaker}'
                                }
                            ];
                            callback(items);
                        }
                    });
                },
                block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre'
            });
        }, 300);
    });

    // Initialize TinyMCE when edit modal is shown
    document.getElementById('editWorkflowModal').addEventListener('shown.bs.modal', function () {
        setTimeout(function() {
            // Remove existing instance first
            if (tinymce.get('edit_email_content')) {
                tinymce.get('edit_email_content').remove();
            }
            // Initialize for edit modal
            initializeTinyMCE();
        }, 300);
    });

    // Clean up TinyMCE when modals are hidden
    document.getElementById('addWorkflowModal').addEventListener('hidden.bs.modal', function () {
        if (tinymce.get('email_content')) {
            tinymce.get('email_content').remove();
        }
    });

    document.getElementById('editWorkflowModal').addEventListener('hidden.bs.modal', function () {
        if (tinymce.get('edit_email_content')) {
            tinymce.get('edit_email_content').remove();
        }
    });
});
</script>

<!-- TinyMCE Free Version -->
<script src="https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js"></script>

@endsection
