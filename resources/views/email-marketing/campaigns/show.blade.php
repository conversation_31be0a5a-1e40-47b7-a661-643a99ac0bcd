@extends('layouts.app')

@section('title', '<PERSON> Tiết <PERSON>ến Dịch - ' . $campaign->name)

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-eye me-2"></i>{{ $campaign->name }}
                    </h1>
                    <p class="text-muted mb-0">Chi tiết chiến dịch email marketing</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại
                    </a>
                    <a href="{{ route('email-marketing.campaigns.edit', $campaign) }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-edit me-2"></i>Chỉnh Sửa
                    </a>
                    <a href="{{ route('email-marketing.workflows', $campaign) }}" class="btn btn-primary">
                        <i class="fas fa-sitemap me-2"></i>Quản Lý Workflow
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Info -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-info-circle me-2"></i>Thông Tin Chiến Dịch
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Tên Chiến Dịch</label>
                            <div class="form-control-plaintext">{{ $campaign->name }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Trạng Thái</label>
                            <div class="form-control-plaintext">
                                @if($campaign->status === 'active')
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-play me-1"></i>Đang hoạt động
                                    </span>
                                @elseif($campaign->status === 'paused')
                                    <span class="badge bg-warning fs-6">
                                        <i class="fas fa-pause me-1"></i>Tạm dừng
                                    </span>
                                @elseif($campaign->status === 'completed')
                                    <span class="badge bg-primary fs-6">
                                        <i class="fas fa-check me-1"></i>Hoàn thành
                                    </span>
                                @else
                                    <span class="badge bg-secondary fs-6">
                                        <i class="fas fa-edit me-1"></i>Nháp
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Webinar Liên Kết</label>
                            <div class="form-control-plaintext">
                                @if($campaign->webinar)
                                    <span class="badge bg-info fs-6">{{ $campaign->webinar->title }}</span>
                                @else
                                    <span class="badge bg-secondary fs-6">Tất cả webinar</span>
                                @endif
                            </div>
                        </div>

                        @if($campaign->description)
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Mô Tả</label>
                            <div class="form-control-plaintext">{{ $campaign->description }}</div>
                        </div>
                        @endif
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Ngày Tạo</label>
                            <div class="form-control-plaintext">{{ $campaign->created_at->format('d/m/Y H:i') }}</div>
                        </div>
                        @if($campaign->started_at)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Ngày Bắt Đầu</label>
                            <div class="form-control-plaintext">{{ $campaign->started_at->format('d/m/Y H:i') }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Thống Kê
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $sent = $campaign->emailLogs->where('status', 'sent')->count();
                        $opened = $campaign->emailLogs->where('status', 'opened')->count();
                        $clicked = $campaign->emailLogs->where('status', 'clicked')->count();
                        $failed = $campaign->emailLogs->where('status', 'failed')->count();

                        $openRate = $sent > 0 ? round(($opened / $sent) * 100, 1) : 0;
                        $clickRate = $sent > 0 ? round(($clicked / $sent) * 100, 1) : 0;
                    @endphp

                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="stats-card stats-card-primary">
                                <div class="stats-icon">
                                    <i class="fas fa-paper-plane"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number">{{ number_format($sent) }}</div>
                                    <div class="stats-label">Email Gửi</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stats-card stats-card-info">
                                <div class="stats-icon">
                                    <i class="fas fa-envelope-open"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number">{{ number_format($opened) }}</div>
                                    <div class="stats-label">Đã Mở</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stats-card stats-card-success">
                                <div class="stats-icon">
                                    <i class="fas fa-mouse-pointer"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number">{{ number_format($clicked) }}</div>
                                    <div class="stats-label">Đã Click</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stats-card stats-card-danger">
                                <div class="stats-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number">{{ number_format($failed) }}</div>
                                    <div class="stats-label">Thất Bại</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="rate-section mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-envelope-open text-info me-2"></i>
                                    <span class="fw-bold">Tỷ lệ mở email</span>
                                </div>
                                <span class="badge bg-info fs-6">{{ $openRate }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-info progress-bar-animated"
                                     style="width: {{ $openRate }}%"
                                     role="progressbar"
                                     aria-valuenow="{{ $openRate }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>

                        <div class="rate-section">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-mouse-pointer text-success me-2"></i>
                                    <span class="fw-bold">Tỷ lệ click</span>
                                </div>
                                <span class="badge bg-success fs-6">{{ $clickRate }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success progress-bar-animated"
                                     style="width: {{ $clickRate }}%"
                                     role="progressbar"
                                     aria-valuenow="{{ $clickRate }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflows -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-sitemap me-2"></i>Workflows ({{ $campaign->workflows->count() }})
                        </h5>
                        <a href="{{ route('email-marketing.workflows', $campaign) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-cog me-2"></i>Quản Lý Workflows
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($campaign->workflows->count() > 0)
                        <div class="row">
                            @foreach($campaign->workflows->sortBy('sequence_order') as $workflow)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                                 style="width: 30px; height: 30px;">
                                                <span class="fw-bold small">{{ $workflow->sequence_order }}</span>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0">{{ $workflow->emailTemplate->subject }}</h6>
                                                <small class="text-muted">
                                                    @if($workflow->scheduled_at)
                                                        Lên lịch: {{ $workflow->scheduled_at->format('d/m/Y H:i') }}
                                                    @else
                                                        Delay:
                                                        @if($workflow->delay_days > 0){{ $workflow->delay_days }}d @endif
                                                        @if($workflow->delay_hours > 0){{ $workflow->delay_hours }}h @endif
                                                        @if($workflow->delay_minutes > 0){{ $workflow->delay_minutes }}m @endif
                                                        @if($workflow->delay_days == 0 && $workflow->delay_hours == 0 && $workflow->delay_minutes == 0)
                                                            Ngay lập tức
                                                        @endif
                                                    @endif
                                                </small>
                                            </div>
                                        </div>
                                        <div class="text-muted small">
                                            {{ Str::limit(strip_tags($workflow->emailTemplate->content), 80) }}
                                        </div>
                                        <div class="mt-2">
                                            <span class="badge {{ $workflow->is_active ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $workflow->is_active ? 'Hoạt động' : 'Tạm dừng' }}
                                            </span>
                                            <span class="badge bg-light text-dark">
                                                {{ $workflow->emailLogs->where('status', 'sent')->count() }} email gửi
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Chưa có workflow nào</h6>
                            <p class="text-muted mb-3">Tạo workflow để bắt đầu gửi email tự động</p>
                            <a href="{{ route('email-marketing.workflows', $campaign) }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Tạo Workflow
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Email Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-history me-2"></i>Email Gần Đây
                        </h5>
                        <a href="{{ route('email-marketing.logs', ['campaign_id' => $campaign->id]) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-2"></i>Xem Tất Cả
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($campaign->emailLogs->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="border-0 px-4 py-3">Người Nhận</th>
                                        <th class="border-0 px-4 py-3">Tiêu Đề</th>
                                        <th class="border-0 px-4 py-3">Trạng Thái</th>
                                        <th class="border-0 px-4 py-3">Thời Gian</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($campaign->emailLogs->take(10) as $log)
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $log->recipient_name ?: 'N/A' }}</div>
                                            <small class="text-muted">{{ $log->recipient_email }}</small>
                                        </td>
                                        <td class="px-4 py-3">{{ Str::limit($log->subject, 50) }}</td>
                                        <td class="px-4 py-3">
                                            @if($log->status === 'sent')
                                                <span class="badge bg-success">Đã gửi</span>
                                            @elseif($log->status === 'opened')
                                                <span class="badge bg-info">Đã mở</span>
                                            @elseif($log->status === 'clicked')
                                                <span class="badge bg-warning">Đã click</span>
                                            @elseif($log->status === 'failed')
                                                <span class="badge bg-danger">Thất bại</span>
                                            @else
                                                <span class="badge bg-secondary">Chờ gửi</span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($log->sent_at)
                                                <div class="small">{{ $log->sent_at->format('d/m/Y H:i') }}</div>
                                                <div class="text-muted small">{{ $log->sent_at->diffForHumans() }}</div>
                                            @else
                                                <div class="small text-muted">{{ $log->created_at->format('d/m/Y H:i') }}</div>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Chưa có email nào được gửi</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    min-height: 80px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.stats-card-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stats-card-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.stats-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
    flex-shrink: 0;
}

.stats-content {
    flex-grow: 1;
}

.stats-number {
    font-size: 1.8rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.progress {
    background-color: #e9ecef;
    border-radius: 10px;
    height: 10px !important;
}

.progress-bar {
    border-radius: 10px;
}

.badge {
    font-size: 0.75em;
}

.card {
    transition: transform 0.2s ease-in-out;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.rate-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #007bff;
}

.rate-section:last-child {
    border-left-color: #28a745;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}
</style>
@endsection
