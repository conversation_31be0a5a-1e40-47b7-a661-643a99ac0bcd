@extends('layouts.app')

@section('title', 'Quản Lý Chiến Dịch Email')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-bullhorn me-2"></i>Quản Lý Chiến Dịch Email
                    </h1>
                    <p class="text-muted mb-0">Tạo và quản lý các chiến dịch email marketing</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại Dashboard
                    </a>
                    <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><PERSON><PERSON>o Chiế<PERSON> Dị<PERSON>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" action="{{ route('email-marketing.campaigns') }}">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">Trạng thái</label>
                                <select name="status" class="form-select">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Nháp</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Đang hoạt động</option>
                                    <option value="paused" {{ request('status') === 'paused' ? 'selected' : '' }}>Tạm dừng</option>
                                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Hoàn thành</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Webinar</label>
                                <select name="webinar_id" class="form-select">
                                    <option value="">Tất cả webinar</option>
                                    @foreach(auth()->user()->webinars as $webinar)
                                        <option value="{{ $webinar->id }}" {{ request('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                            {{ $webinar->title }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Tìm kiếm</label>
                                <input type="text" name="search" class="form-control" placeholder="Tìm theo tên chiến dịch..." 
                                       value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Lọc
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-list me-2"></i>Danh Sách Chiến Dịch
                        </h5>
                        <div class="text-muted small">
                            Tổng: {{ $campaigns->total() }} chiến dịch
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($campaigns->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="border-0 px-4 py-3">Tên Chiến Dịch</th>
                                        <th class="border-0 px-4 py-3">Webinar</th>
                                        <th class="border-0 px-4 py-3">Trạng Thái</th>
                                        <th class="border-0 px-4 py-3">Workflows</th>
                                        <th class="border-0 px-4 py-3">Email Gửi</th>
                                        <th class="border-0 px-4 py-3">Tỷ Lệ Mở</th>
                                        <th class="border-0 px-4 py-3">Ngày Tạo</th>
                                        <th class="border-0 px-4 py-3">Hành Động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($campaigns as $campaign)
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $campaign->name }}</div>
                                            @if($campaign->description)
                                                <small class="text-muted">{{ Str::limit($campaign->description, 50) }}</small>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($campaign->webinar)
                                                <span class="badge bg-info">{{ Str::limit($campaign->webinar->title, 30) }}</span>
                                            @else
                                                <span class="badge bg-secondary">Tất cả webinar</span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($campaign->status === 'active')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-play me-1"></i>Đang hoạt động
                                                </span>
                                            @elseif($campaign->status === 'paused')
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-pause me-1"></i>Tạm dừng
                                                </span>
                                            @elseif($campaign->status === 'completed')
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-check me-1"></i>Hoàn thành
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-edit me-1"></i>Nháp
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="badge bg-light text-dark">
                                                {{ $campaign->workflows->count() }} workflow
                                            </span>
                                        </td>
                                        <td class="px-4 py-3">
                                            @php
                                                $sent = $campaign->emailLogs->where('status', 'sent')->count();
                                                $total = $campaign->emailLogs->count();
                                            @endphp
                                            <div class="fw-bold">{{ number_format($sent) }}</div>
                                            <small class="text-muted">/ {{ number_format($total) }} tổng</small>
                                        </td>
                                        <td class="px-4 py-3">
                                            @php
                                                $sent = $campaign->emailLogs->where('status', 'sent')->count();
                                                $opened = $campaign->emailLogs->where('status', 'opened')->count();
                                                $rate = $sent > 0 ? round(($opened / $sent) * 100, 1) : 0;
                                            @endphp
                                            <div class="fw-bold">{{ $rate }}%</div>
                                            <div class="progress" style="height: 4px;">
                                                <div class="progress-bar bg-success" style="width: {{ $rate }}%"></div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="small">{{ $campaign->created_at->format('d/m/Y') }}</div>
                                            <div class="text-muted small">{{ $campaign->created_at->format('H:i') }}</div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" 
                                                   class="btn btn-outline-primary" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('email-marketing.campaigns.edit', $campaign) }}" 
                                                   class="btn btn-outline-secondary" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ route('email-marketing.workflows', $campaign) }}" 
                                                   class="btn btn-outline-info" title="Quản lý workflow">
                                                    <i class="fas fa-sitemap"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteCampaign({{ $campaign->id }})" title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($campaigns->hasPages())
                            <div class="card-footer bg-white border-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="text-muted small">
                                        Hiển thị {{ $campaigns->firstItem() }} - {{ $campaigns->lastItem() }} 
                                        trong tổng số {{ $campaigns->total() }} kết quả
                                    </div>
                                    {{ $campaigns->links() }}
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Không tìm thấy chiến dịch nào</h5>
                            <p class="text-muted">Tạo chiến dịch email marketing đầu tiên của bạn</p>
                            <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Tạo Chiến Dịch
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác Nhận Xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa chiến dịch này không?</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Hành động này không thể hoàn tác và sẽ xóa tất cả dữ liệu liên quan.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.table tbody tr:hover {
    background-color: #f8f9fa;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.progress {
    background-color: #e9ecef;
}

.badge {
    font-size: 0.75em;
}
</style>

<script>
function deleteCampaign(campaignId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/email-marketing/campaigns/${campaignId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endsection
