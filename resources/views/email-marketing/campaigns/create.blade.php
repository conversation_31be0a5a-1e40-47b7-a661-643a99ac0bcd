@extends('layouts.app')

@section('title', 'Tạo <PERSON> Dịch Email Mới')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-plus me-2"></i>Tạo Chiến Dịch Email Mới
                    </h1>
                    <p class="text-muted mb-0">Thiết lập chiến dịch email marketing cho webinar của bạn</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>Thông Tin Chiến Dịch
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('email-marketing.campaigns.store') }}" method="POST">
                        @csrf

                        <!-- Campaign Name -->
                        <div class="mb-4">
                            <label for="name" class="form-label fw-bold">
                                Tên Chiến Dịch <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}"
                                   placeholder="Nhập tên chiến dịch..." required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Tên mô tả ngắn gọn cho chiến dịch của bạn</div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="description" class="form-label fw-bold">Ghi Chú</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Mô tả chi tiết về chiến dịch...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Mô tả mục đích và nội dung của chiến dịch</div>
                        </div>

                        <!-- Webinar Selection -->
                        <div class="mb-4">
                            <label for="webinar_id" class="form-label fw-bold">
                                Chọn Webinar <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('webinar_id') is-invalid @enderror"
                                    id="webinar_id" name="webinar_id" required>
                                <option value="">-- Chọn webinar để lấy danh sách data --</option>
                                @foreach($webinars as $webinar)
                                    <option value="{{ $webinar->id }}" {{ old('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                        {{ $webinar->title }}
                                        @if($webinar->schedules)
                                            @php
                                                $schedules = is_array($webinar->schedules) ? $webinar->schedules : json_decode($webinar->schedules, true);
                                                $nextSchedule = collect($schedules)->first();
                                            @endphp
                                            @if($nextSchedule)
                                                - {{ \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->format('d/m/Y H:i') }}
                                            @endif
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            @error('webinar_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Chọn webinar để lấy danh sách người đăng ký làm đối tượng gửi email</div>
                        </div>



                        <!-- Status -->
                        <div class="mb-4">
                            <label for="status" class="form-label fw-bold">Trạng Thái Ban Đầu</label>
                            <select class="form-select @error('status') is-invalid @enderror"
                                    id="status" name="status">
                                <option value="draft" {{ old('status', 'draft') === 'draft' ? 'selected' : '' }}>
                                    Nháp - Chưa kích hoạt
                                </option>
                                <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>
                                    Hoạt động - Bắt đầu ngay
                                </option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Bạn có thể thay đổi trạng thái sau khi tạo chiến dịch</div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Tạo Chiến Dịch
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Next Steps Info -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light py-3">
                    <h6 class="mb-0 text-primary">
                        <i class="fas fa-info-circle me-2"></i>Bước Tiếp Theo
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 40px; height: 40px;">
                                    <span class="fw-bold">1</span>
                                </div>
                                <div>
                                    <div class="fw-bold">Tạo Workflow</div>
                                    <small class="text-muted">Thiết lập chuỗi email tự động</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 40px; height: 40px;">
                                    <span class="fw-bold">2</span>
                                </div>
                                <div>
                                    <div class="fw-bold">Soạn Email</div>
                                    <small class="text-muted">Tạo nội dung email với personalization</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 40px; height: 40px;">
                                    <span class="fw-bold">3</span>
                                </div>
                                <div>
                                    <div class="fw-bold">Kích Hoạt</div>
                                    <small class="text-muted">Bắt đầu gửi email tự động</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.form-check-input:checked + .form-check-label .card {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.form-check-input {
    position: absolute;
    opacity: 0;
}

.form-check-label {
    cursor: pointer;
    width: 100%;
}

.form-check-label:hover .card {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
}
</style>

<script>
// Auto-select radio when clicking on card
document.querySelectorAll('.form-check-label').forEach(label => {
    label.addEventListener('click', function() {
        const radio = this.querySelector('.form-check-input') ||
                     document.querySelector(`#${this.getAttribute('for')}`);
        if (radio) {
            radio.checked = true;
        }
    });
});
</script>
@endsection
