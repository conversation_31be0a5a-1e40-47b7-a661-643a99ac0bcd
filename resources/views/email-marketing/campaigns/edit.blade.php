@extends('layouts.app')

@section('title', 'Chỉnh Sử<PERSON>n D<PERSON> - ' . $campaign->name)

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-edit me-2"></i>Chỉnh Sửa Chiến Dịch
                    </h1>
                    <p class="text-muted mb-0">Cập nhật thông tin chiến dịch: {{ $campaign->name }}</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>Thông Tin Chiến Dịch
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('email-marketing.campaigns.update', $campaign) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Campaign Name -->
                        <div class="mb-4">
                            <label for="name" class="form-label fw-bold">
                                Tên Chiến Dịch <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name', $campaign->name) }}"
                                   placeholder="Nhập tên chiến dịch..." required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Tên mô tả ngắn gọn cho chiến dịch của bạn</div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="description" class="form-label fw-bold">Ghi Chú</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Mô tả chi tiết về chiến dịch...">{{ old('description', $campaign->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Mô tả mục đích và nội dung của chiến dịch</div>
                        </div>

                        <!-- Webinar Selection -->
                        <div class="mb-4">
                            <label for="webinar_id" class="form-label fw-bold">
                                Chọn Webinar <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('webinar_id') is-invalid @enderror"
                                    id="webinar_id" name="webinar_id" required>
                                <option value="">-- Chọn webinar để lấy danh sách data --</option>
                                @foreach($webinars as $webinar)
                                    <option value="{{ $webinar->id }}" {{ old('webinar_id', $campaign->webinar_id) == $webinar->id ? 'selected' : '' }}>
                                        {{ $webinar->title }}
                                        @if($webinar->schedules)
                                            @php
                                                $schedules = is_array($webinar->schedules) ? $webinar->schedules : json_decode($webinar->schedules, true);
                                                $nextSchedule = collect($schedules)->first();
                                            @endphp
                                            @if($nextSchedule)
                                                - {{ \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->format('d/m/Y H:i') }}
                                            @endif
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            @error('webinar_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Chọn webinar để lấy danh sách người đăng ký làm đối tượng gửi email</div>
                        </div>



                        <!-- Status -->
                        <div class="mb-4">
                            <label for="status" class="form-label fw-bold">Trạng Thái</label>
                            <select class="form-select @error('status') is-invalid @enderror"
                                    id="status" name="status">
                                <option value="draft" {{ old('status', $campaign->status) === 'draft' ? 'selected' : '' }}>
                                    Nháp - Chưa kích hoạt
                                </option>
                                <option value="active" {{ old('status', $campaign->status) === 'active' ? 'selected' : '' }}>
                                    Hoạt động - Đang chạy
                                </option>
                                <option value="paused" {{ old('status', $campaign->status) === 'paused' ? 'selected' : '' }}>
                                    Tạm dừng
                                </option>
                                <option value="completed" {{ old('status', $campaign->status) === 'completed' ? 'selected' : '' }}>
                                    Hoàn thành
                                </option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Trạng thái hiện tại của chiến dịch</div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Cập Nhật Chiến Dịch
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Warning for Active Campaigns -->
            @if($campaign->status === 'active')
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-warning text-dark py-3">
                        <h6 class="mb-0 fw-bold">
                            <i class="fas fa-exclamation-triangle me-2"></i>Lưu Ý Quan Trọng
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">Chiến dịch này đang ở trạng thái <strong>HOẠT ĐỘNG</strong>. Khi bạn thay đổi thông tin:</p>
                        <ul class="mb-3">
                            <li>Các email đã được lên lịch sẽ không bị ảnh hưởng</li>
                            <li>Chỉ các trigger events mới sẽ sử dụng cấu hình mới</li>
                            <li>Nếu thay đổi trigger type, có thể ảnh hưởng đến workflow</li>
                        </ul>
                        <p class="mb-0 text-warning">
                            <strong>Khuyến nghị:</strong> Tạm dừng chiến dịch trước khi thay đổi cấu hình quan trọng.
                        </p>
                    </div>
                </div>
            @endif

            <!-- Campaign Statistics -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light py-3">
                    <h6 class="mb-0 text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Thống Kê Hiện Tại
                    </h6>
                </div>
                <div class="card-body">
                    @php
                        $sent = $campaign->emailLogs->where('status', 'sent')->count();
                        $opened = $campaign->emailLogs->where('status', 'opened')->count();
                        $clicked = $campaign->emailLogs->where('status', 'clicked')->count();
                        $failed = $campaign->emailLogs->where('status', 'failed')->count();
                    @endphp
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="p-3 bg-primary bg-opacity-10 rounded">
                                <div class="h5 mb-0 text-primary">{{ number_format($sent) }}</div>
                                <small class="text-muted">Đã Gửi</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="p-3 bg-info bg-opacity-10 rounded">
                                <div class="h5 mb-0 text-info">{{ number_format($opened) }}</div>
                                <small class="text-muted">Đã Mở</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                <div class="h5 mb-0 text-success">{{ number_format($clicked) }}</div>
                                <small class="text-muted">Đã Click</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="p-3 bg-danger bg-opacity-10 rounded">
                                <div class="h5 mb-0 text-danger">{{ number_format($failed) }}</div>
                                <small class="text-muted">Thất Bại</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-opacity-10 {
    --bs-bg-opacity: 0.1;
}

.form-label {
    color: #495057;
}

.card-header h6 {
    color: inherit;
}
</style>
@endsection
