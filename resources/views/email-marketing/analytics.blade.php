@extends('layouts.app')

@section('title', 'Báo Cáo Email Marketing')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Báo Cáo Email Marketing
                    </h1>
                    <p class="text-muted mb-0">Phân tích hiệu suất và thống kê chi tiết các chiến dịch email</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay Lại Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row mb-4">
        @php
            $totalSent = $campaigns->sum(function($campaign) {
                return $campaign->emailLogs->where('status', 'sent')->count();
            });
            $totalOpened = $campaigns->sum(function($campaign) {
                return $campaign->emailLogs->where('status', 'opened')->count();
            });
            $totalClicked = $campaigns->sum(function($campaign) {
                return $campaign->emailLogs->where('status', 'clicked')->count();
            });
            $totalFailed = $campaigns->sum(function($campaign) {
                return $campaign->emailLogs->where('status', 'failed')->count();
            });

            $openRate = $totalSent > 0 ? round(($totalOpened / $totalSent) * 100, 2) : 0;
            $clickRate = $totalSent > 0 ? round(($totalClicked / $totalSent) * 100, 2) : 0;
            $failureRate = $totalSent > 0 ? round(($totalFailed / $totalSent) * 100, 2) : 0;
        @endphp

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Tổng Email Gửi</div>
                            <div class="h2 mb-0">{{ number_format($totalSent) }}</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Tỷ Lệ Mở Email</div>
                            <div class="h2 mb-0">{{ $openRate }}%</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-envelope-open"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Tỷ Lệ Click</div>
                            <div class="h2 mb-0">{{ $clickRate }}%</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-mouse-pointer"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Tỷ Lệ Thất Bại</div>
                            <div class="h2 mb-0">{{ $failureRate }}%</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-chart-line me-2"></i>Hiệu Suất Theo Chiến Dịch
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if($campaigns->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="border-0 px-4 py-3">Tên Chiến Dịch</th>
                                        <th class="border-0 px-4 py-3">Webinar</th>
                                        <th class="border-0 px-4 py-3">Email Gửi</th>
                                        <th class="border-0 px-4 py-3">Đã Mở</th>
                                        <th class="border-0 px-4 py-3">Đã Click</th>
                                        <th class="border-0 px-4 py-3">Thất Bại</th>
                                        <th class="border-0 px-4 py-3">Tỷ Lệ Mở</th>
                                        <th class="border-0 px-4 py-3">Tỷ Lệ Click</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($campaigns as $campaign)
                                    @php
                                        $sent = $campaign->emailLogs->where('status', 'sent')->count();
                                        $opened = $campaign->emailLogs->where('status', 'opened')->count();
                                        $clicked = $campaign->emailLogs->where('status', 'clicked')->count();
                                        $failed = $campaign->emailLogs->where('status', 'failed')->count();

                                        $campaignOpenRate = $sent > 0 ? round(($opened / $sent) * 100, 1) : 0;
                                        $campaignClickRate = $sent > 0 ? round(($clicked / $sent) * 100, 1) : 0;
                                    @endphp
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $campaign->name }}</div>
                                            <span class="badge {{ $campaign->status === 'active' ? 'bg-success' : ($campaign->status === 'paused' ? 'bg-warning' : 'bg-secondary') }}">
                                                {{ ucfirst($campaign->status) }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($campaign->webinar)
                                                <span class="badge bg-info">{{ Str::limit($campaign->webinar->title, 30) }}</span>
                                            @else
                                                <span class="badge bg-secondary">Tất cả webinar</span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold text-primary">{{ number_format($sent) }}</div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold text-info">{{ number_format($opened) }}</div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold text-success">{{ number_format($clicked) }}</div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold text-danger">{{ number_format($failed) }}</div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $campaignOpenRate }}%</div>
                                            <div class="progress" style="height: 4px;">
                                                <div class="progress-bar bg-info" style="width: {{ $campaignOpenRate }}%"></div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $campaignClickRate }}%</div>
                                            <div class="progress" style="height: 4px;">
                                                <div class="progress-bar bg-success" style="width: {{ $campaignClickRate }}%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có dữ liệu báo cáo</h5>
                            <p class="text-muted">Tạo chiến dịch và gửi email để xem báo cáo chi tiết</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-lightbulb me-2"></i>Thông Tin Hữu Ích
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <div class="me-3">
                                    <i class="fas fa-trophy fa-2x text-warning"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Chiến Dịch Hiệu Quả Nhất</div>
                                    @php
                                        $bestCampaign = $campaigns->sortByDesc(function($campaign) {
                                            $sent = $campaign->emailLogs->where('status', 'sent')->count();
                                            $opened = $campaign->emailLogs->where('status', 'opened')->count();
                                            return $sent > 0 ? ($opened / $sent) * 100 : 0;
                                        })->first();
                                    @endphp
                                    @if($bestCampaign)
                                        <small class="text-muted">{{ $bestCampaign->name }}</small>
                                    @else
                                        <small class="text-muted">Chưa có dữ liệu</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <div class="me-3">
                                    <i class="fas fa-clock fa-2x text-info"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Thời Gian Tốt Nhất</div>
                                    <small class="text-muted">Phân tích dựa trên tỷ lệ mở email</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <div class="me-3">
                                    <i class="fas fa-users fa-2x text-success"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Tổng Người Nhận</div>
                                    @php
                                        $totalRecipients = $campaigns->sum(function($campaign) {
                                            return $campaign->emailLogs->groupBy('recipient_email')->count();
                                        });
                                    @endphp
                                    <small class="text-muted">{{ number_format($totalRecipients) }} người</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-chart-pie me-2"></i>Phân Bố Trạng Thái Email
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="analytics-card analytics-card-success">
                                <div class="analytics-icon">
                                    <i class="fas fa-paper-plane"></i>
                                </div>
                                <div class="analytics-content">
                                    <div class="analytics-number">{{ number_format($totalSent) }}</div>
                                    <div class="analytics-label">Đã Gửi</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="analytics-card analytics-card-info">
                                <div class="analytics-icon">
                                    <i class="fas fa-envelope-open"></i>
                                </div>
                                <div class="analytics-content">
                                    <div class="analytics-number">{{ number_format($totalOpened) }}</div>
                                    <div class="analytics-label">Đã Mở</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="analytics-card analytics-card-warning">
                                <div class="analytics-icon">
                                    <i class="fas fa-mouse-pointer"></i>
                                </div>
                                <div class="analytics-content">
                                    <div class="analytics-number">{{ number_format($totalClicked) }}</div>
                                    <div class="analytics-label">Đã Click</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="analytics-card analytics-card-danger">
                                <div class="analytics-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="analytics-content">
                                    <div class="analytics-number">{{ number_format($totalFailed) }}</div>
                                    <div class="analytics-label">Thất Bại</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.analytics-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    min-height: 80px;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.analytics-card-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.analytics-card-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.analytics-card-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.analytics-card-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.analytics-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
    flex-shrink: 0;
}

.analytics-content {
    flex-grow: 1;
}

.analytics-number {
    font-size: 1.8rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 5px;
}

.analytics-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.progress {
    background-color: #e9ecef;
    border-radius: 10px;
}

.badge {
    font-size: 0.75em;
}

.card {
    transition: transform 0.2s ease-in-out;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}
</style>
@endsection
