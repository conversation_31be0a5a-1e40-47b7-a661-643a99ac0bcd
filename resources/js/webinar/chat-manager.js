class ChatManager {
    constructor(config) {
        this.config = {
            apiEndpoint: config.apiEndpoint,
            pollingInterval: config.pollingInterval || 3000,
            csrfToken: $('meta[name="csrf-token"]').attr("content"),
            liveVideoElement: $("#native-live-video"),
            regularVideoElement: $("#native-video"),
            commentForm: $("#comment-form"),
            commentInput: $("#comment-input"),
            messagesContainer: $("#chat-messages"),
            messagesWrapper: $(".messages-wrapper"),
            joinNotificationContainer: $("#join-notification-container"),
            newCommentContainer: $("#new-comment-container"),
        };

        this.lastCommentId = 0;
        this.seenComments = new Set();
        this.isFetching = false;
        this.seededCheckInterval = null;

        this._bindEvents();
        this._initializeComments();
    }

    _formatTimeWithHours(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        return `${hours.toString().padStart(2, "0")}:${minutes
            .toString()
            .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }

    _scrollChatToBottom() {
        if (this.config.messagesContainer.length) {
            this.config.messagesContainer.animate(
                { scrollTop: this.config.messagesContainer[0].scrollHeight },
                "smooth"
            );
        } else if (this.config.messagesWrapper.length) {
            this.config.messagesWrapper.animate(
                { scrollTop: this.config.messagesWrapper[0].scrollHeight },
                "smooth"
            );
        }
    }

    _addCommentToUI(comment) {
        if (!comment.id) {
            console.log("Comment missing ID, skipping");
            return;
        }

        // Check if comment already exists in DOM
        if ($(`.chat-message[data-id="${comment.id}"]`).length) {
            console.log(
                `Comment ${comment.id} already exists in DOM, skipping`
            );
            return;
        }

        console.log(`Adding comment ${comment.id} to UI:`, comment);

        const $commentElement = $("<div>")
            .addClass("chat-message")
            .attr("data-id", comment.id);

        let timestampHtml = "";
        if (comment.video_timestamp) {
            timestampHtml = ` <small>(tại ${this._formatTimeWithHours(
                comment.video_timestamp
            )})</small>`;
        }

        $commentElement.html(`
                <div class="meta">
                    <span class="user-badge">
                        <i class="fas fa-user"></i>
                    </span>
                    <div class="name-time">
                        <span class="name">
                            ${
                                comment.participant
                                    ? comment.participant.name
                                    : "Khách"
                            }
                        </span>
                        <!--<span class="time">${
                            comment.created_at
                        }${timestampHtml}</span>-->
                    </div>
                </div>
                <div class="content">${comment.content}</div>
            `);

        if (
            this.config.messagesWrapper.length &&
            this.config.newCommentContainer.length
        ) {
            $commentElement.insertBefore(this.config.newCommentContainer);
        } else if (this.config.messagesContainer.length) {
            this.config.messagesContainer.append($commentElement);
        }
    }

    async _fetchComments() {
        if (this.isFetching) {
            // console.log("Fetch in progress, skipping.");
            return;
        }
        this.isFetching = true;

        let currentVideoTimestamp = 0;

        // Check for YouTube player first
        if (window.youtubePlayer && window.youtubePlayer.getCurrentTime) {
            currentVideoTimestamp = Math.floor(
                window.youtubePlayer.getCurrentTime()
            );
            // console.log('Using YouTube timestamp:', currentVideoTimestamp);
        }
        // Fallback to native video elements
        else if (
            this.config.liveVideoElement.length &&
            !this.config.liveVideoElement[0].paused
        ) {
            currentVideoTimestamp = Math.floor(
                this.config.liveVideoElement[0].currentTime
            );
        } else if (
            this.config.regularVideoElement.length &&
            !this.config.regularVideoElement[0].paused
        ) {
            currentVideoTimestamp = Math.floor(
                this.config.regularVideoElement[0].currentTime
            );
        }

        const videoIsActive =
            (window.youtubePlayer &&
                window.youtubePlayer.getPlayerState &&
                window.youtubePlayer.getPlayerState() === 1) || // YouTube playing
            (this.config.liveVideoElement.length &&
                !this.config.liveVideoElement[0].paused) ||
            (this.config.regularVideoElement.length &&
                !this.config.regularVideoElement[0].paused) ||
            (this.config.regularVideoElement.length &&
                this.config.regularVideoElement[0].currentTime > 0);

        if (
            !videoIsActive &&
            currentVideoTimestamp <= 0 &&
            !webinarConfig.isLivestream
        ) {
            // console.log(
            //     "Video not active and no valid timestamp, skipping fetch."
            // );
            this.isFetching = false;
            return;
        }

        try {
            const response = await fetch(
                `${this.config.apiEndpoint.fetchComments}?duration=${currentVideoTimestamp}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": this.config.csrfToken,
                        "X-Requested-With": "XMLHttpRequest",
                    },
                }
            );
            let comments = await response.json();

            comments = comments.filter((comment) => comment !== null);
            if (comments && comments.length > 0) {
                console.log(`Received ${comments.length} comments from server`);
                console.log(`Current lastCommentId: ${this.lastCommentId}`);

                // Filter comments that haven't been seen yet (not in seenComments set)
                const newComments = comments.filter(
                    (comment) =>
                        comment.id && !this.seenComments.has(comment.id)
                );

                if (newComments.length > 0) {
                    console.log(
                        `Found ${newComments.length} new comments to add`
                    );

                    // Sort by ID to ensure correct order
                    newComments.sort((a, b) => a.id - b.id);

                    // Add each comment and track it
                    newComments.forEach((comment) => {
                        this._addCommentToUI(comment);
                        // Add to seenComments after successfully adding to UI
                        this.seenComments.add(comment.id);
                    });

                    // Update lastCommentId to the highest ID we've seen
                    const allIds = comments.map((c) => c.id).filter((id) => id);
                    if (allIds.length > 0) {
                        const maxId = Math.max(...allIds);
                        if (maxId > this.lastCommentId) {
                            this.lastCommentId = maxId;
                            console.log(
                                `Updated lastCommentId to ${this.lastCommentId}`
                            );
                        }
                    }

                    this._scrollChatToBottom();
                } else {
                    console.log("No new comments to add");
                }
            }
        } catch (error) {
            console.error("Error fetching comments:", error);
        } finally {
            this.isFetching = false;
            setTimeout(
                () => this._fetchComments(),
                this.config.pollingInterval
            );
        }
    }

    async _postComment(content, videoTimestamp) {
        // Show loading state
        const submitBtn = $("#comment-submit-btn");
        submitBtn.prop("disabled", true);
        submitBtn.find(".btn-text").addClass("d-none");
        submitBtn.find(".btn-loading").removeClass("d-none");

        try {
            const response = await fetch(this.config.apiEndpoint.postComment, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": this.config.csrfToken,
                },
                body: JSON.stringify({
                    content: content,
                    video_timestamp: videoTimestamp,
                }),
            });
            const data = await response.json();

            if (data.success) {
                this.config.commentInput.val("");

                console.log("Post comment success:", data);

                if (data.comment.id) {
                    if (data.comment.id > this.lastCommentId) {
                        this.lastCommentId = data.comment.id;
                        console.log(
                            `Updated lastCommentId to ${this.lastCommentId} from postComment`
                        );
                    }
                }

                const now = new Date();
                const timeString = now.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                });
                const clientSideComment = {
                    id: data.comment.id,
                    participant: { name: data.comment.name },
                    created_at: timeString,
                    content: data.comment.content,
                    video_timestamp: videoTimestamp,
                };

                // Add comment to UI and track it
                this._addCommentToUI(clientSideComment);
                if (data.comment.id) {
                    this.seenComments.add(data.comment.id);
                }
                this._scrollChatToBottom();
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Lỗi",
                    text:
                        data.message ||
                        "Không thể gửi bình luận. Vui lòng thử lại.",
                });
            }
        } catch (error) {
            console.error("Error posting comment:", error);
            Swal.fire({
                icon: "error",
                title: "Lỗi",
                text: "Có lỗi xảy ra khi gửi bình luận. Vui lòng thử lại sau.",
            });
        } finally {
            // Reset button state regardless of success or failure
            submitBtn.prop("disabled", false);
            submitBtn.find(".btn-text").removeClass("d-none");
            submitBtn.find(".btn-loading").addClass("d-none");
            $("#main-content").scrollTop(0);
        }
    }

    _handleCommentSubmit(e) {
        e.preventDefault();
        const content = this.config.commentInput.val().trim();
        if (content === "") return;

        let currentVideoTimestamp = 0;
        if (
            this.config.liveVideoElement.length &&
            !this.config.liveVideoElement[0].paused
        ) {
            currentVideoTimestamp = Math.floor(
                this.config.liveVideoElement[0].currentTime
            );
        } else if (
            this.config.regularVideoElement.length &&
            !this.config.regularVideoElement[0].paused
        ) {
            currentVideoTimestamp = Math.floor(
                this.config.regularVideoElement[0].currentTime
            );
        }
        this._postComment(content, currentVideoTimestamp);
    }

    _initializeComments() {
        if (this.config.messagesContainer.length) {
            const $existingComments = this.config.messagesContainer.find(
                ".chat-message[data-id]"
            );
            // console.log(
            //     `Found ${$existingComments.length} existing comments in the DOM`
            // );

            $existingComments.each((index, el) => {
                const id = parseInt($(el).data("id"), 10);
                if (!isNaN(id)) this.seenComments.add(id);
            });

            if ($existingComments.length > 0) {
                const ids = $existingComments
                    .map((index, el) => parseInt($(el).data("id"), 10))
                    .get()
                    .filter((id) => !isNaN(id));
                if (ids.length > 0) this.lastCommentId = Math.max(...ids);
            }
            // console.log(
            //     `Initialized with ${this.seenComments.size} existing comments, last ID: ${this.lastCommentId}`
            // );

            this._fetchComments();
            // setInterval(
            //     () => this._fetchComments(),
            //     this.config.pollingInterval
            // );

            setTimeout(() => this._scrollChatToBottom(), 100);
        }
    }

    _bindEvents() {
        if (this.config.commentForm.length) {
            this.config.commentForm.on(
                "submit",
                this._handleCommentSubmit.bind(this)
            );

            // Add event handler for Enter key on the comment input
            this.config.commentInput.on("keydown", (e) => {
                // Check if Enter was pressed without Shift key (Shift+Enter for new line)
                if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault(); // Prevent default Enter behavior (new line)
                    this.config.commentForm.submit(); // Submit the form
                }
            });
        }
    }
}

$(document).ready(function () {
    // Instantiate the ChatManager
    if (typeof apiEndpoint !== "undefined") {
        // Ensure apiEndpoint is defined
        new ChatManager({
            apiEndpoint: apiEndpoint, // Assuming apiEndpoint is globally available
            pollingInterval: 5000,
        });
    } else {
        console.error(
            "ChatManager: apiEndpoint is not defined. Chat functionality will not work."
        );
    }

    // Fake participant simulation
    function simulateFakeParticipants(changeAmount = 0) {
        // Vietnamese name components for dynamic name generation
        const vietnameseLastNames = [
            "Nguyễn",
            "Trần",
            "Lê",
            "Phạm",
            "Hoàng",
            "Huỳnh",
            "Phan",
            "Vũ",
            "Võ",
            "Đặng",
            "Bùi",
            "Đỗ",
            "Hồ",
            "Ngô",
            "Dương",
            "Lý",
            "Đào",
            "Đinh",
            "Trịnh",
            "Mai",
            "Lưu",
            "Tạ",
        ];

        const vietnameseMiddleNames = [
            "Văn",
            "Thị",
            "Hữu",
            "Đức",
            "Quang",
            "Công",
            "Minh",
            "Thanh",
            "Thành",
            "Như",
            "Hoài",
            "Ngọc",
            "Kim",
            "Hồng",
            "Đình",
            "Xuân",
            "Quốc",
            "Việt",
            "Bảo",
            "Anh",
        ];

        const vietnameseMaleFirstNames = [
            "Hùng",
            "Dũng",
            "Tùng",
            "Thắng",
            "Cường",
            "Hưng",
            "Huy",
            "Nam",
            "Quân",
            "Trung",
            "Hiếu",
            "Long",
            "Phúc",
            "Hải",
            "Phong",
            "Bình",
            "Thành",
            "Đạt",
            "Tú",
            "Vũ",
            "Minh",
            "Hoàng",
            "Khang",
            "Tuấn",
            "Đức",
            "Thịnh",
            "Khánh",
            "Tâm",
            "Kiên",
            "Lâm"
        ];

        const vietnameseFemaleFirstNames = [
            "Hương",
            "Lan",
            "Thảo",
            "Mai",
            "Trang",
            "Hà",
            "Anh",
            "Ngọc",
            "Linh",
            "Chi",
            "Yến",
            "Phương",
            "Hiền",
            "Nhung",
            "Oanh",
            "Hạnh",
            "Quyên",
            "Diệp",
            "My",
            "Hoa",
            "Thu",
            "Loan",
            "Thúy",
            "Xuân",
            "Huyền",
            "Giang",
            "Ly",
            "Nga",
            "Vy",
            "Nhi"
        ];

        // Generate a unique Vietnamese name by combining components
        function generateVietnameseName() {
            const lastName =
                vietnameseLastNames[
                    Math.floor(Math.random() * vietnameseLastNames.length)
                ];
            const middleName =
                vietnameseMiddleNames[
                    Math.floor(Math.random() * vietnameseMiddleNames.length)
                ];
            const firstName =
                vietnameseFirstNames[
                    Math.floor(Math.random() * vietnameseFirstNames.length)
                ];

            // Vietnamese names typically follow the format: Last Middle First
            return `${lastName} ${middleName} ${firstName}`;
        }

        // Already joined fake participants
        const joinedParticipants = new Set();

        // Show join message in the fixed notification above the input

        var hideJoinMessageTimeout = null;
        function addJoinMessage(name) {
            if (hideJoinMessageTimeout) {
                clearTimeout(hideJoinMessageTimeout);
            }

            // Update the fixed notification instead of adding to chat
            const $joinContainer = $("#join-notification-container");
            $joinContainer.fadeIn(100);

            if ($joinContainer.length) {
                $joinContainer.html(
                    `<i class="fas fa-user"></i> <strong>${name}</strong> đã tham gia`
                );
                $joinContainer.css("display", "block");

                // Make sure chat scrolls to see the notification
                // scrollChatToBottom();
            } else {
                console.error("Join notification container not found!");
            }

            // Also boost the viewer count
            boostViewerCount();

            hideJoinMessageTimeout = setTimeout(() => {
                $joinContainer.fadeOut(100);
            }, 1800);
        }

        // Add viewer count when new participants join
        function boostViewerCount() {
            // Access the viewer count animation variables
            if (typeof currentCount !== "undefined") {
                // Increase by a random amount between 1-3
                const increase = Math.floor(Math.random() * 3) + 1;
                currentCount += increase;

                // Update the display if we have the function
                if (typeof updateViewerCountDisplay === "function") {
                    updateViewerCountDisplay(currentCount);
                }
            }
        }

        // Process the immediate change amount
        if (changeAmount > 0) {
            // Immediately add participants based on changeAmount
            for (let i = 0; i < changeAmount; i++) {
                const name = generateVietnameseName();
                joinedParticipants.add(name);

                // Add join messages with a small delay between each to make it look natural
                setTimeout(() => {
                    addJoinMessage(name);
                }, i * 800); // Shorter delays between each join (800ms)
            }
        }

        // Periodically add new participants
        function scheduleNewParticipants() {
            // Generate a new unique name
            let newParticipant;
            do {
                newParticipant = generateVietnameseName();
            } while (joinedParticipants.has(newParticipant));

            // Add to joined set
            joinedParticipants.add(newParticipant);

            // Show join message
            addJoinMessage(newParticipant);

            // Schedule next participant join
            // Higher frequency at the beginning, then slow down
            const joinedCount = joinedParticipants.size;
            let nextJoinDelay;

            if (joinedCount < 10) {
                // Join every 20-40 seconds for first 10 people
                nextJoinDelay = 20000 + Math.floor(Math.random() * 20000);
            } else if (joinedCount < 20) {
                // Join every 40-90 seconds for next 10 people
                nextJoinDelay = 40000 + Math.floor(Math.random() * 50000);
            } else {
                // Join every 2-4 minutes after that
                nextJoinDelay = 120000 + Math.floor(Math.random() * 120000);
            }

            // Schedule next participant
            setTimeout(scheduleNewParticipants, nextJoinDelay);
        }
    }

    function createFakeViewCounter(
        min,
        max,
        selector,
        storageKey,
        maxTimeThreshold = "00:10:00",
        onUpdate
    ) {
        let viewCount = min;

        // Display initial count
        $(selector).text(viewCount);

        // Function to calculate time-based view count
        function calculateViewCount(currentVideoTime) {
            // Parse the video time (HH:mm:ss)
            const [hours, minutes, seconds] = currentVideoTime
                .split(":")
                .map(Number);
            const totalSeconds = hours * 3600 + minutes * 60 + seconds;

            // Parse the threshold time
            const [thresholdHours, thresholdMinutes, thresholdSeconds] =
                maxTimeThreshold.split(":").map(Number);
            const thresholdTotalSeconds =
                thresholdHours * 3600 +
                thresholdMinutes * 60 +
                thresholdSeconds;

            if (totalSeconds >= thresholdTotalSeconds) {
                // After threshold, stay at max with small fluctuations
                return max;
            } else if (totalSeconds <= 0) {
                // At the start, use min
                return min;
            } else {
                // Gradually increase from min to max
                const progress = totalSeconds / thresholdTotalSeconds;
                const targetCount = min + Math.floor(progress * (max - min));
                return targetCount;
            }
        }

        // Function to update viewer count
        function updateViewCount(currentVideoTime) {
            // Get base count from video time
            const baseCount = calculateViewCount(currentVideoTime);

            // Add natural fluctuation
            const now = new Date();
            const hour = now.getHours();

            // Higher chance to increase during peak hours (8-11am, 1-4pm)
            let increaseChance = 0.65; // Default probability

            if ((hour >= 8 && hour <= 11) || (hour >= 13 && hour <= 16)) {
                increaseChance = 0.85; // Higher probability during peak hours
            } else if (hour >= 0 && hour <= 5) {
                increaseChance = 0.35; // Lower probability during night
            }

            // If we're at max views, set equal probability for increase/decrease (50/50)
            if (baseCount >= max) {
                increaseChance = 0.4;
            }

            // Decide whether to increase or decrease
            const shouldIncrease = Math.random() < increaseChance;

            // Random change amount (1-3)
            const changeAmount = Math.floor(Math.random() * 3) + 1;

            // Apply fluctuation
            let newViewCount;
            if (baseCount <= max && shouldIncrease) {
                newViewCount = Math.min(baseCount + changeAmount, max);
                onUpdate?.(changeAmount);
            } else if (baseCount > min && !shouldIncrease) {
                newViewCount = Math.max(baseCount - changeAmount, min);
            } else {
                newViewCount = baseCount;
            }

            // Update the UI
            $(selector).text(newViewCount);
            viewCount = newViewCount;

            // Schedule next update after random interval (2-5 seconds)
            const nextInterval = Math.floor(Math.random() * 3000) + 2000;
            setTimeout(() => {
                // Get current video time for next update
                let currentTime = "00:00:00";

                if (
                    $("#native-live-video").length &&
                    !$("#native-live-video")[0].paused
                ) {
                    const videoSeconds =
                        Math.floor($("#native-live-video")[0].currentTime) || 0;
                    currentTime = formatTimeWithHours(
                        isNaN(videoSeconds) ? 0 : videoSeconds
                    );
                } else if (
                    $("#native-video").length &&
                    !$("#native-video")[0].paused
                ) {
                    const videoSeconds =
                        Math.floor($("#native-video")[0].currentTime) || 0;
                    currentTime = formatTimeWithHours(
                        isNaN(videoSeconds) ? 0 : videoSeconds
                    );
                }

                updateViewCount(currentTime);
            }, nextInterval);
        }

        // Helper function to format seconds to HH:mm:ss
        function formatTimeWithHours(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            return `${hours
                .toString()
                .padStart(
                    2,
                    "0"
                )}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
        }

        // Start updating after a random initial delay
        const initialDelay = Math.floor(Math.random() * 2000) + 1000;
        setTimeout(() => {
            // Get initial video time
            let initialTime = "00:00:00";

            if (
                $("#native-live-video").length &&
                !$("#native-live-video")[0].paused
            ) {
                const videoSeconds = Math.floor(
                    $("#native-live-video")[0].currentTime
                );
                initialTime = formatTimeWithHours(videoSeconds);
            } else if (
                $("#native-video").length &&
                !$("#native-video")[0].paused
            ) {
                const videoSeconds = Math.floor(
                    $("#native-video")[0].currentTime
                );
                initialTime = formatTimeWithHours(videoSeconds);
            }

            updateViewCount(initialTime);
        }, 100);
    }
    // Start fake participant simulation if we're in a live webinar
    let [minViews, maxViews] = webinarInfo?.virtual_viewers
        ? webinarInfo?.virtual_viewers?.split("-").map(Number)
        : [
              95 + Math.floor(Math.random() * 26),
              195 + Math.floor(Math.random() * 17),
          ];

    if (!maxViews) {
        maxViews = minViews;
        minViews = 0;
    }

    // Only run fake viewer simulation for livestreams
    if (webinarConfig?.isLiveSimulate) {
        setTimeout(() => {
            createFakeViewCounter(
                minViews,
                maxViews + Math.floor(Math.random() * 5) + 1, // Add random 1-5 views to maxViews
                "#viewerCount",
                `webinar_${webinarInfo.id}_viewcount`,
                "00:10:00", // Time threshold when view count reaches max
                (changeAmount) => {
                    console.log(`Lượt xem tăng: +${changeAmount}`);
                    simulateFakeParticipants(changeAmount);
                }
            );
        }, 3000);
    }
});
