# 📧 HƯỚNG DẪN TESTING - HỆ THỐNG EMAIL MARKETING

## 🎯 **TỔNG QUAN HỆ THỐNG**

Hệ thống Email Marketing cho phép tạo các chiến dịch email tự động gửi đến người đăng ký webinar theo lịch trình cụ thể.

### **Luồng Hoạt Động:**
```
1. Tạo Campaign → 2. Tạo Workflows → 3. Lên lịch gửi → 4. Hệ thống tự động gửi
```

---

## 🚀 **HƯỚNG DẪN TESTING CHI TIẾT**

### **BƯỚC 1: CHUẨN BỊ DỮ LIỆU TEST**

#### **1.1. Tạo Webinar Test**
```
- Truy cập: /webinars/create
- Tạo webinar với tên: "Webinar Test Email Marketing"
- Thêm ít nhất 2-3 participants (người đăng ký)
- Lưu lại ID webinar để test
```

#### **1.2. Kiểm tra Permissions**
```
- <PERSON><PERSON><PERSON> nhập với user có quyền "email marketing"
- <PERSON><PERSON><PERSON> tra menu "Email Marketing" hiển thị trong nhóm "Marketing"
```

---

### **BƯỚC 2: TEST TẠO CAMPAIGN**

#### **2.1. Truy cập trang Email Marketing**
```
URL: /email-marketing
✅ Kiểm tra: Dashboard hiển thị thống kê
✅ Kiểm tra: Cards thống kê có màu sắc đẹp
✅ Kiểm tra: Responsive trên mobile
```

#### **2.2. Tạo Campaign mới**
```
URL: /email-marketing/campaigns/create

📝 Test Case 1: Tạo campaign thành công
- Tên: "Test Campaign 1"
- Ghi chú: "Campaign test cho webinar ABC"
- Chọn webinar: Webinar đã tạo ở bước 1.1
- Trạng thái: "Draft"
- Click "Tạo Campaign"

✅ Expected: Redirect đến trang campaigns với thông báo thành công
✅ Expected: Campaign xuất hiện trong danh sách
```

```
📝 Test Case 2: Validation errors
- Để trống tên campaign → Kiểm tra error message
- Không chọn webinar → Kiểm tra error message
- Tên quá dài (>255 ký tự) → Kiểm tra error message
```

---

### **BƯỚC 3: TEST QUẢN LÝ WORKFLOWS**

#### **3.1. Truy cập Workflow Management**
```
- Từ danh sách campaigns, click "Quản Lý Workflow"
- Hoặc truy cập: /email-marketing/campaigns/{campaign_id}/workflows

✅ Kiểm tra: Giao diện timeline đẹp
✅ Kiểm tra: Nút "Thêm Workflow" hiển thị
✅ Kiểm tra: Thông tin campaign hiển thị đúng
```

#### **3.2. Tạo Workflow đầu tiên**
```
📝 Test Case 3: Tạo workflow email chào mừng
- Click "Thêm Workflow"
- Thứ tự: 1
- Ngày gửi: Chọn ngày mai
- Giờ gửi: 09:00
- Tiêu đề: "Chào mừng {name} đến với {webinar_title}!"
- Nội dung: 
  "Xin chào {name}!
   Cảm ơn bạn đã đăng ký webinar {webinar_title}.
   Link tham gia: {join_url}"
- Click "Lưu Workflow"

✅ Expected: Workflow xuất hiện trong timeline
✅ Expected: Hiển thị thời gian "Lên lịch: [ngày]/[tháng]/[năm] 09:00"
✅ Expected: Badge "Hoạt động" màu xanh
```

#### **3.3. Tạo thêm Workflows**
```
📝 Test Case 4: Tạo workflow nhắc nhở
- Thứ tự: 2
- Ngày gửi: Ngày mai + 1 ngày
- Giờ gửi: 19:00
- Tiêu đề: "Nhắc nhở: {webinar_title} sắp bắt đầu!"
- Nội dung: Email nhắc nhở

📝 Test Case 5: Tạo workflow follow-up
- Thứ tự: 3
- Ngày gửi: Ngày mai + 2 ngày
- Giờ gửi: 21:00
- Tiêu đề: "Cảm ơn bạn đã tham gia {webinar_title}"
- Nội dung: Email cảm ơn
```

---

### **BƯỚC 4: TEST CHỨC NĂNG CHỈNH SỬA**

#### **4.1. Test Edit Workflow**
```
📝 Test Case 6: Chỉnh sửa workflow
- Click nút "Edit" (icon bút chì) trên workflow
- Modal popup hiển thị với dữ liệu đã điền sẵn
- Thay đổi thời gian gửi: 10:00 → 14:30
- Thay đổi tiêu đề email
- Click "Lưu Thay Đổi"

✅ Expected: Modal đóng, trang reload
✅ Expected: Thời gian hiển thị cập nhật: "14:30"
✅ Expected: Tiêu đề mới hiển thị trong timeline
```

#### **4.2. Test Toggle Status**
```
📝 Test Case 7: Tạm dừng workflow
- Click nút "Pause" (icon pause) trên workflow
- Confirm dialog hiển thị
- Click "OK"

✅ Expected: Badge chuyển thành "Tạm dừng" màu xám
✅ Expected: Icon chuyển thành "Play"

📝 Test Case 8: Kích hoạt lại workflow
- Click nút "Play" trên workflow đã tạm dừng
- Confirm và kiểm tra trạng thái chuyển về "Hoạt động"
```

#### **4.3. Test View Details**
```
📝 Test Case 9: Xem chi tiết workflow
- Click nút "Eye" (icon mắt) trên workflow
- Modal hiển thị thông tin chi tiết:
  + Thông tin cơ bản (thứ tự, trạng thái, thời gian)
  + Thống kê (email đã gửi, đã mở, đã click)
  + Nội dung email (tiêu đề + content)

✅ Expected: Modal hiển thị đầy đủ thông tin
✅ Expected: Thống kê hiển thị số 0 (chưa gửi email)
```

---

### **BƯỚC 5: TEST HỆ THỐNG TỰ ĐỘNG**

#### **5.1. Kích hoạt Campaign**
```
📝 Test Case 10: Chuyển campaign sang Active
- Truy cập: /email-marketing/campaigns/{campaign_id}/edit
- Thay đổi trạng thái từ "Draft" → "Active"
- Click "Cập Nhật Campaign"

✅ Expected: Campaign status = "Active"
✅ Expected: Badge hiển thị "Active" màu xanh
```

#### **5.2. Test Command Processing**
```
📝 Test Case 11: Chạy command xử lý email
Terminal: php artisan email:process-campaigns --dry-run

✅ Expected: Hiển thị "Processing Email Campaigns..."
✅ Expected: Hiển thị "DRY RUN MODE - No emails will be sent"
✅ Expected: Hiển thị số lượng campaigns được xử lý
✅ Expected: Không có lỗi

📝 Test Case 12: Chạy command thực tế (cẩn thận!)
Terminal: php artisan email:process-campaigns

⚠️  CHÚ Ý: Command này sẽ tạo email logs thực tế
✅ Expected: Tạo email logs cho participants
✅ Expected: Status = "pending" hoặc "sent"
```

#### **5.3. Kiểm tra Email Logs**
```
📝 Test Case 13: Xem logs email
- Truy cập: /email-marketing/logs
- Kiểm tra danh sách email logs

✅ Expected: Hiển thị emails đã được tạo
✅ Expected: Thông tin recipient, subject, status
✅ Expected: Filter và search hoạt động
```

---

### **BƯỚC 6: TEST EDGE CASES**

#### **6.1. Test Validation**
```
📝 Test Case 14: Ngày trong quá khứ
- Tạo workflow với ngày gửi = hôm qua
✅ Expected: Error "Ngày phải >= hôm nay"

📝 Test Case 15: Thứ tự trùng lặp
- Tạo 2 workflows cùng thứ tự = 1
✅ Expected: Cho phép (hệ thống sẽ sắp xếp theo thời gian)
```

#### **6.2. Test Permissions**
```
📝 Test Case 16: User không có quyền
- Đăng nhập user không có permission "email marketing"
- Truy cập /email-marketing
✅ Expected: Redirect hoặc 403 error
```

#### **6.3. Test Delete**
```
📝 Test Case 17: Xóa workflow
- Click nút "Trash" trên workflow
- Confirm dialog hiển thị
- Click "OK"
✅ Expected: Workflow biến mất khỏi timeline
✅ Expected: Email logs liên quan bị xóa
```

---

### **BƯỚC 7: TEST RESPONSIVE & UX**

#### **7.1. Test Mobile**
```
📝 Test Case 18: Giao diện mobile
- Mở trên điện thoại hoặc DevTools mobile view
- Kiểm tra tất cả trang: dashboard, campaigns, workflows
✅ Expected: Layout responsive, không bị vỡ
✅ Expected: Buttons và modals hoạt động tốt
```

#### **7.2. Test Performance**
```
📝 Test Case 19: Load time
- Kiểm tra thời gian load các trang
✅ Expected: < 2 giây cho mỗi trang
✅ Expected: Không có lỗi JavaScript console
```

---

## 🐛 **CHECKLIST LỖI THƯỜNG GẶP**

### **Database Issues:**
- [ ] Migration chạy thành công
- [ ] Tất cả tables tồn tại
- [ ] Foreign keys đúng
- [ ] Permissions được seed

### **Frontend Issues:**
- [ ] Bootstrap CSS/JS load đúng
- [ ] JavaScript không có lỗi
- [ ] Modals hiển thị đúng
- [ ] Forms submit thành công

### **Backend Issues:**
- [ ] Routes hoạt động
- [ ] Controllers trả về đúng data
- [ ] Validation rules đúng
- [ ] Authorization checks đúng

### **Email System:**
- [ ] Command chạy không lỗi
- [ ] Email logs được tạo
- [ ] Variables được replace đúng
- [ ] Scheduled tasks hoạt động

---

## 📊 **KẾT QUẢ MONG ĐỢI**

Sau khi test xong, hệ thống phải:

✅ **Tạo được campaigns và workflows**
✅ **Lên lịch gửi email theo thời gian cụ thể**
✅ **Hệ thống tự động quét và tạo email logs**
✅ **Giao diện đẹp, responsive, dễ sử dụng**
✅ **Chỉnh sửa workflows hoạt động tốt**
✅ **Thống kê hiển thị chính xác**
✅ **Không có lỗi JavaScript hoặc PHP**

---

## 🆘 **TROUBLESHOOTING**

### **Nếu gặp lỗi:**

1. **Kiểm tra logs:** `storage/logs/laravel.log`
2. **Kiểm tra database:** Đảm bảo migrations đã chạy
3. **Kiểm tra permissions:** User có quyền "email marketing"
4. **Kiểm tra JavaScript:** Mở DevTools console
5. **Chạy command test:** `php artisan email:process-campaigns --dry-run`

### **Commands hữu ích:**
```bash
# Kiểm tra routes
php artisan route:list --name=email-marketing

# Chạy migrations
php artisan migrate

# Seed permissions
php artisan db:seed --class=RolesAndPermissionsSeeder

# Test email processing
php artisan email:process-campaigns --dry-run
```

---

**🎯 Happy Testing! 🎯**
