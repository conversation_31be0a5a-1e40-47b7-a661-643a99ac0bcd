<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Setting;
use Illuminate\Support\Facades\Config;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only run this in web requests, not in console commands or migrations
        if ($this->app->runningInConsole()) {
            return;
        }

        try {
            // Load mail configuration from database settings
            $mailConfig = [
                'driver'     => Setting::get('mail_mailer', Config::get('mail.mailer')),
                'host'       => Setting::get('mail_host', Config::get('mail.host')),
                'port'       => Setting::get('mail_port', Config::get('mail.port')),
                'username'   => Setting::get('mail_username', Config::get('mail.username')),
                'password'   => Setting::get('mail_password', Config::get('mail.password')),
                'encryption' => Setting::get('mail_encryption', Config::get('mail.encryption')),
                'from'       => [
                    'address' => Setting::get('mail_from_address', Config::get('mail.from.address')),
                    'name'    => Setting::get('mail_from_name', Config::get('mail.from.name')),
                ],
            ];

            // Set the configuration at runtime for Laravel 9+
            Config::set('mail.default', $mailConfig['driver']); // Key fix!
            Config::set('mail.mailer', $mailConfig['driver']);
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $mailConfig['host']);
            Config::set('mail.mailers.smtp.port', (int)($mailConfig['port'] ?: 587));
            Config::set('mail.mailers.smtp.username', $mailConfig['username']);
            Config::set('mail.mailers.smtp.password', $mailConfig['password']);
            Config::set('mail.mailers.smtp.encryption', $mailConfig['encryption']);
            Config::set('mail.from.address', $mailConfig['from']['address']);
            Config::set('mail.from.name', $mailConfig['from']['name']);

        } catch (\Exception $e) {
            // Log the error but don't crash the application
            \Log::error('Failed to load mail configuration from database: ' . $e->getMessage());
        }
    }
}
