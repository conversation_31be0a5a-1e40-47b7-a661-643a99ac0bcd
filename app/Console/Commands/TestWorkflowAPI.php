<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailWorkflow;
use App\Http\Controllers\EmailMarketingController;
use Illuminate\Http\Request;

class TestWorkflowAPI extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:workflow-api {workflow_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test workflow API response';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $workflowId = $this->argument('workflow_id');

        $workflow = EmailWorkflow::find($workflowId);

        if (!$workflow) {
            $this->error("Workflow {$workflowId} not found");
            return 1;
        }

        // Fake authentication
        \Auth::loginUsingId($workflow->campaign->user_id);

        // Simulate the API call
        $controller = new EmailMarketingController();
        $response = $controller->showWorkflow($workflow);

        $data = $response->getData(true);

        $this->info("🔍 API Response for Workflow {$workflowId}:");
        $this->line("Raw response: " . json_encode($data, JSON_PRETTY_PRINT));

        if (isset($data['success'])) {
            $this->line("Success: " . ($data['success'] ? 'true' : 'false'));
        }

        if (isset($data['data']['stats'])) {
            $stats = $data['data']['stats'];
            $this->newLine();
            $this->info("📊 Statistics:");
            $this->line("  Total Sent: " . $stats['total_sent']);
            $this->line("  Total Opened: " . $stats['total_opened']);
            $this->line("  Total Clicked: " . $stats['total_clicked']);
            $this->line("  Total Failed: " . $stats['total_failed']);
        } elseif (isset($data['stats'])) {
            $stats = $data['stats'];
            $this->newLine();
            $this->info("📊 Statistics:");
            $this->line("  Total Sent: " . $stats['total_sent']);
            $this->line("  Total Opened: " . $stats['total_opened']);
            $this->line("  Total Clicked: " . $stats['total_clicked']);
            $this->line("  Total Failed: " . $stats['total_failed']);
        }

        return 0;
    }
}
