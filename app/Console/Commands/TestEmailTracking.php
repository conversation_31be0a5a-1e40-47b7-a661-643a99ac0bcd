<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;

class TestEmailTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-tracking {log_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email tracking by simulating open and click';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $logId = $this->argument('log_id');

        $emailLog = EmailLog::find($logId);

        if (!$emailLog) {
            $this->error("Email log with ID {$logId} not found.");
            return 1;
        }

        $this->info("Testing tracking for Email Log ID: {$logId}");
        $this->line("Email: {$emailLog->recipient_email}");
        $this->line("Status: {$emailLog->status}");
        $this->newLine();

        // Test email open tracking
        if ($emailLog->status === 'sent') {
            $this->info("🔍 Simulating email open...");
            $emailLog->markAsOpened();
            $this->info("✅ Email marked as opened!");

            // Test click tracking
            $this->info("🔍 Simulating link click...");
            $emailLog->markAsClicked();
            $this->info("✅ Email marked as clicked!");

            // Refresh and show updated stats
            $emailLog->refresh();
            $this->newLine();
            $this->info("📊 Updated Stats:");
            $this->line("Status: {$emailLog->status}");
            $this->line("Open Count: {$emailLog->open_count}");
            $this->line("Click Count: {$emailLog->click_count}");
            $this->line("Opened At: " . ($emailLog->opened_at ? $emailLog->opened_at->format('Y-m-d H:i:s') : 'Not opened'));
            $this->line("Clicked At: " . ($emailLog->clicked_at ? $emailLog->clicked_at->format('Y-m-d H:i:s') : 'Not clicked'));

        } else {
            $this->warn("Email status is '{$emailLog->status}', not 'sent'. Cannot track opens/clicks.");
        }

        return 0;
    }
}
