<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Models\EmailWorkflow;
use App\Models\Setting;
use Illuminate\Support\Facades\Config;

class TestWorkflowEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:test-email {workflow_id : ID of workflow to test} {email : Email to send to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending email from a specific workflow';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $workflowId = $this->argument('workflow_id');
        $email = $this->argument('email');

        // Load mail config from database
        $this->loadMailConfig();

        // Find workflow
        $workflow = EmailWorkflow::with(['emailTemplate', 'campaign.webinar'])->find($workflowId);

        if (!$workflow) {
            $this->error("❌ Workflow ID {$workflowId} not found");
            return 1;
        }

        $this->info("🔧 Testing workflow: {$workflow->emailTemplate->subject}");
        $this->line("Campaign: {$workflow->campaign->name}");
        $this->line("Webinar: " . ($workflow->campaign->webinar->title ?? 'N/A'));
        $this->newLine();

        try {
            $template = $workflow->emailTemplate;
            $webinar = $workflow->campaign->webinar;

            // Prepare test variables
            $variables = [
                'name' => 'Test User',
                'email' => $email,
                'phone' => '0123456789',
                'webinar_title' => $webinar ? $webinar->title : 'Test Webinar',
                'webinar_speaker' => $webinar ? $webinar->speaker : 'Test Speaker',
                'join_url' => $webinar ? route('join.show', $webinar->join_code) : '#',
                'join_code' => $webinar ? $webinar->join_code : 'TEST123',
            ];

            // Replace variables in template
            $processedTemplate = $template->replaceVariables($variables);

            $this->line("📧 Sending email...");
            $this->line("Subject: " . $processedTemplate['subject']);
            $this->line("To: " . $email);
            $this->newLine();

            // Send email
            Mail::raw($processedTemplate['content'], function ($message) use ($email, $processedTemplate) {
                $message->to($email)
                        ->subject('[WORKFLOW TEST] ' . $processedTemplate['subject'])
                        ->from(config('mail.mailers.smtp.username'), config('mail.from.name'));
            });

            $this->info("✅ Email sent successfully!");
            $this->line("📬 Check your inbox at: " . $email);

        } catch (\Exception $e) {
            $this->error("❌ Error sending email: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    private function loadMailConfig()
    {
        try {
            $mailConfig = [
                'driver'     => Setting::get('mail_mailer', 'smtp'),
                'host'       => Setting::get('mail_host', 'smtp.gmail.com'),
                'port'       => Setting::get('mail_port', 587),
                'username'   => Setting::get('mail_username'),
                'password'   => Setting::get('mail_password'),
                'encryption' => Setting::get('mail_encryption', 'tls'),
                'from_address' => Setting::get('mail_from_address'),
                'from_name'    => Setting::get('mail_from_name'),
            ];

            // Set the configuration at runtime
            Config::set('mail.default', $mailConfig['driver']);
            Config::set('mail.mailer', $mailConfig['driver']);
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $mailConfig['host']);
            Config::set('mail.mailers.smtp.port', (int)($mailConfig['port'] ?: 587));
            Config::set('mail.mailers.smtp.username', $mailConfig['username']);
            Config::set('mail.mailers.smtp.password', $mailConfig['password']);
            Config::set('mail.mailers.smtp.encryption', $mailConfig['encryption']);
            Config::set('mail.from.address', $mailConfig['from_address']);
            Config::set('mail.from.name', $mailConfig['from_name']);

            $this->line("✅ Mail config loaded from database");

        } catch (\Exception $e) {
            $this->warn('⚠️  Could not load mail config from database: ' . $e->getMessage());
        }
    }
}
