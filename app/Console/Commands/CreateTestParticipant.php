<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Webinar;
use App\Models\WebinarParticipant;

class CreateTestParticipant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:create-participant {email : Email address} {--name=Test User : Name of participant}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test participant for email marketing testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->option('name');

        // Get first webinar
        $webinar = Webinar::first();

        if (!$webinar) {
            $this->error('❌ No webinar found. Please create a webinar first.');
            return 1;
        }

        // Check if participant already exists
        $existing = WebinarParticipant::where('webinar_id', $webinar->id)
                                     ->where('email', $email)
                                     ->first();

        if ($existing) {
            $this->warn("⚠️  Participant with email {$email} already exists for webinar: {$webinar->title}");
            $this->line("Participant ID: {$existing->id}");
            return 0;
        }

        // Create participant
        $participant = WebinarParticipant::create([
            'webinar_id' => $webinar->id,
            'name' => $name,
            'email' => $email,
            'phone' => '0123456789',
        ]);

        $this->info("✅ Test participant created successfully!");
        $this->line("Participant ID: {$participant->id}");
        $this->line("Name: {$participant->name}");
        $this->line("Email: {$participant->email}");
        $this->line("Webinar: {$webinar->title}");

        $this->newLine();
        $this->line("🚀 Now run: php artisan email:process-campaigns");

        return 0;
    }
}
