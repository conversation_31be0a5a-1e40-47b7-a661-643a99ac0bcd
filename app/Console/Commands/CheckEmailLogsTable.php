<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CheckEmailLogsTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:check-email-logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if email_logs table exists and show structure';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking email_logs table...');

        try {
            // Check if table exists
            if (Schema::hasTable('email_logs')) {
                $this->info('✅ Table email_logs exists');

                // Get table structure
                $columns = Schema::getColumnListing('email_logs');
                $this->info('📋 Columns: ' . implode(', ', $columns));

                // Count records
                $count = DB::table('email_logs')->count();
                $this->info("📊 Records: {$count}");

                // Show recent records
                if ($count > 0) {
                    $recent = DB::table('email_logs')
                                ->select('id', 'recipient_email', 'status', 'open_count', 'click_count', 'created_at')
                                ->orderBy('id', 'desc')
                                ->limit(5)
                                ->get();

                    $this->newLine();
                    $this->info('📧 Recent records:');
                    foreach ($recent as $record) {
                        $this->line("ID: {$record->id} | Email: {$record->recipient_email} | Status: {$record->status} | Opens: {$record->open_count} | Clicks: {$record->click_count}");
                    }
                }

            } else {
                $this->error('❌ Table email_logs does not exist');

                // Check pending migrations
                $this->info('🔍 Checking pending migrations...');
                $migrations = DB::table('migrations')
                                ->where('migration', 'like', '%email_logs%')
                                ->get();

                if ($migrations->count() > 0) {
                    $this->info('📋 Found email_logs migrations:');
                    foreach ($migrations as $migration) {
                        $this->line("- {$migration->migration} (batch: {$migration->batch})");
                    }
                } else {
                    $this->warn('⚠️  No email_logs migrations found in migrations table');
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Error checking table: ' . $e->getMessage());
        }

        return 0;
    }
}
