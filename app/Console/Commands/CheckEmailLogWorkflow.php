<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;
use App\Models\EmailWorkflow;

class CheckEmailLogWorkflow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:check-workflow {log_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check email log workflow details';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $logId = $this->argument('log_id');

        $log = EmailLog::find($logId);

        if (!$log) {
            $this->error("Email log {$logId} not found");
            return 1;
        }

        $this->info("📧 Email Log Details:");
        $this->line("ID: {$log->id}");
        $this->line("Email: {$log->recipient_email}");
        $this->line("Status: {$log->status}");
        $this->line("Workflow ID: {$log->workflow_id}");
        $this->line("Campaign ID: {$log->campaign_id}");
        $this->line("Scheduled At: {$log->scheduled_at}");

        if ($log->workflow) {
            $this->newLine();
            $this->info("🔄 Workflow Details:");
            $this->line("ID: {$log->workflow->id}");
            $this->line("Sequence Order: {$log->workflow->sequence_order}");
            $this->line("Scheduled At: {$log->workflow->scheduled_at}");
            $this->line("Is Active: " . ($log->workflow->is_active ? 'Yes' : 'No'));

            if ($log->workflow->emailTemplate) {
                $this->newLine();
                $this->info("📝 Email Template:");
                $this->line("Subject: {$log->workflow->emailTemplate->subject}");
                $this->line("Content: " . substr($log->workflow->emailTemplate->content, 0, 100) . "...");
            }
        }

        return 0;
    }
}
