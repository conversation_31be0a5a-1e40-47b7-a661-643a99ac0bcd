<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;

class CheckEmailLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:check-logs {--limit=10}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check recent email logs and tracking status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = $this->option('limit');

        $this->info("📧 Recent Email Logs (Last {$limit}):");
        $this->newLine();

        $logs = EmailLog::with(['campaign', 'participant'])
                        ->latest()
                        ->take($limit)
                        ->get();

        if ($logs->isEmpty()) {
            $this->warn('No email logs found.');
            return;
        }

        $headers = ['ID', 'Email', 'Status', 'Opens', 'Clicks', 'Sent At', 'Campaign'];
        $rows = [];

        foreach ($logs as $log) {
            $rows[] = [
                $log->id,
                $log->recipient_email,
                $log->status,
                $log->open_count,
                $log->click_count,
                $log->sent_at ? $log->sent_at->format('Y-m-d H:i:s') : 'Not sent',
                $log->campaign->name ?? 'N/A'
            ];
        }

        $this->table($headers, $rows);

        // Show tracking URLs for latest log
        $latestLog = $logs->first();
        if ($latestLog) {
            $this->newLine();
            $this->info("🔗 Tracking URLs for Email ID {$latestLog->id}:");
            $this->line("Open tracking: " . route('email-marketing.track.open', ['log' => $latestLog->id]));
            $this->line("Click tracking: " . route('email-marketing.track.click', ['log' => $latestLog->id, 'url' => base64_encode('https://example.com')]));
        }

        // Show statistics
        $totalSent = EmailLog::where('status', 'sent')->count();
        $totalOpened = EmailLog::where('open_count', '>', 0)->count();
        $totalClicked = EmailLog::where('click_count', '>', 0)->count();

        $openRate = $totalSent > 0 ? round(($totalOpened / $totalSent) * 100, 2) : 0;
        $clickRate = $totalSent > 0 ? round(($totalClicked / $totalSent) * 100, 2) : 0;

        $this->newLine();
        $this->info("📊 Overall Statistics:");
        $this->line("Total Sent: {$totalSent}");
        $this->line("Total Opened: {$totalOpened}");
        $this->line("Total Clicked: {$totalClicked}");
        $this->line("Open Rate: {$openRate}%");
        $this->line("Click Rate: {$clickRate}%");

        return 0;
    }
}
