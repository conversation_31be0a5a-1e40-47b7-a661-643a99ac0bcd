<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use App\Models\Setting;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mime\Email as SymfonyEmail;
use Exception;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email : Email address to send test email to} {--debug : Show detailed debug information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to check SMTP configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('❌ Email không hợp lệ: ' . $email);
            return 1;
        }

        // Load mail configuration from database (same as MailConfigServiceProvider)
        $this->loadMailConfigFromDatabase();

        $this->info('🚀 Đang gửi email test đến: ' . $email);
        $this->newLine();

        try {
            // Show current mail configuration
            $this->line('📧 Cấu hình SMTP hiện tại:');
            $this->line('   Host: ' . config('mail.mailers.smtp.host'));
            $this->line('   Port: ' . config('mail.mailers.smtp.port'));
            $this->line('   Username: ' . config('mail.mailers.smtp.username'));
            $this->line('   Encryption: ' . config('mail.mailers.smtp.encryption'));
            $this->line('   From: ' . config('mail.from.address'));
            $this->newLine();

            // Debug mode
            if ($this->option('debug')) {
                $this->line('🔍 Debug Mode - Chi tiết cấu hình:');
                $this->line('   Password: ' . (config('mail.mailers.smtp.password') ? '***có***' : '***trống***'));
                $this->line('   Mailer: ' . config('mail.mailer'));
                $this->line('   From Name: ' . config('mail.from.name'));
                $this->newLine();
            }

            // Test SMTP connection first
            $this->line('🔌 Đang test kết nối SMTP...');
            $this->testSmtpConnection();

            // Send test email with detailed error catching
            $this->line('📤 Đang gửi email...');

            // Capture any Swift/Symfony mailer errors
            $sent = false;
            $errorMessage = '';

            try {
                // Enable debug logging for mail
                if ($this->option('debug')) {
                    Config::set('mail.mailers.smtp.stream', [
                        'ssl' => [
                            'verify_peer' => false,
                            'verify_peer_name' => false,
                            'allow_self_signed' => true,
                        ],
                    ]);
                    $this->line('🔧 Đã bật debug mode cho SMTP');
                }

                // Force reload mail config to ensure it's using database settings
                $this->reloadMailConfig();

                if ($this->option('debug')) {
                    $this->line('🔍 Final config check:');
                    $this->line('   Transport: ' . config('mail.default'));
                    $this->line('   SMTP Host: ' . config('mail.mailers.smtp.host'));
                    $this->line('   SMTP Port: ' . config('mail.mailers.smtp.port'));
                    $this->line('   SMTP User: ' . config('mail.mailers.smtp.username'));
                    $this->line('   From Address: ' . config('mail.from.address'));
                }

                // Send email via Laravel Mail
                Mail::raw($this->getTestEmailContent(), function ($message) use ($email) {
                    $message->to($email)
                            ->subject('🧪 Test Email từ ' . config('app.name'))
                            ->from(config('mail.mailers.smtp.username'), config('mail.from.name')); // Use SMTP username as from
                });
                $sent = true;

            } catch (\Swift_TransportException $e) {
                $errorMessage = 'Swift Transport Error: ' . $e->getMessage();
            } catch (\Symfony\Component\Mailer\Exception\TransportException $e) {
                $errorMessage = 'Symfony Transport Error: ' . $e->getMessage();
            } catch (\Exception $e) {
                $errorMessage = 'General Error: ' . $e->getMessage();
            }

            if (!$sent) {
                $this->error('❌ Lỗi gửi email: ' . $errorMessage);
                if ($this->option('debug')) {
                    $this->line('🔍 Chi tiết lỗi: ' . $errorMessage);
                }
                return 1;
            }

            $this->info('✅ Email test đã được gửi thành công!');
            $this->line('📬 Kiểm tra hộp thư của bạn tại: ' . $email);
            $this->line('📝 Lưu ý: Có thể email nằm trong thư mục Spam/Junk');

            // Show additional debug info
            if ($this->option('debug')) {
                $this->newLine();
                $this->line('🔍 Thông tin debug thêm:');
                $this->line('   - Kiểm tra log tại: storage/logs/laravel.log');
                $this->line('   - Nếu dùng Gmail, đảm bảo sử dụng App Password');
                $this->line('   - Kiểm tra firewall/antivirus có block port 587 không');
            }

            return 0;

        } catch (Exception $e) {
            $this->error('❌ Lỗi khi gửi email:');
            $this->line('   ' . $e->getMessage());
            $this->newLine();

            $this->warn('💡 Các bước kiểm tra:');
            $this->line('   1. Kiểm tra cấu hình SMTP trong file .env');
            $this->line('   2. Đảm bảo MAIL_PASSWORD là App Password (với Gmail)');
            $this->line('   3. Kiểm tra firewall/network connection');
            $this->line('   4. Xem log chi tiết trong storage/logs/laravel.log');

            return 1;
        }
    }

    /**
     * Test SMTP connection
     */
    private function testSmtpConnection()
    {
        try {
            $host = config('mail.mailers.smtp.host');
            $port = config('mail.mailers.smtp.port');

            if ($this->option('debug')) {
                $this->line("   Đang kết nối tới {$host}:{$port}...");
            }

            $connection = @fsockopen($host, $port, $errno, $errstr, 10);

            if (!$connection) {
                $this->warn("⚠️  Không thể kết nối tới SMTP server: {$errstr} ({$errno})");
                return false;
            }

            fclose($connection);
            $this->line('✅ Kết nối SMTP thành công');
            return true;

        } catch (\Exception $e) {
            $this->warn('⚠️  Lỗi test kết nối: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email via Symfony Mailer directly
     */
    private function sendViaSwiftMailer($email)
    {
        try {
            // Create Symfony Transport
            $dsn = sprintf(
                'smtp://%s:%s@%s:%d',
                urlencode(config('mail.mailers.smtp.username')),
                urlencode(config('mail.mailers.smtp.password')),
                config('mail.mailers.smtp.host'),
                config('mail.mailers.smtp.port')
            );

            if (config('mail.mailers.smtp.encryption') === 'tls') {
                $dsn .= '?encryption=tls';
            }

            $this->line('🔗 DSN: ' . preg_replace('/:[^:@]*@/', ':***@', $dsn));

            $transport = EsmtpTransport::fromDsn($dsn);
            $mailer = new Mailer($transport);

            // Create Message
            $message = (new SymfonyEmail())
                ->from(config('mail.mailers.smtp.username'))
                ->to($email)
                ->subject('🧪 Test Email từ Symfony Mailer')
                ->text($this->getTestEmailContent());

            // Send
            $mailer->send($message);
            $this->info('✅ Symfony Mailer: Email sent successfully!');

        } catch (\Exception $e) {
            $this->error('❌ Symfony Mailer Error: ' . $e->getMessage());
            $this->line('   Stack trace: ' . $e->getTraceAsString());
        }
    }

    /**
     * Reload mail configuration to ensure fresh settings
     */
    private function reloadMailConfig()
    {
        // Clear any cached mail manager
        app()->forgetInstance('mail.manager');
        app()->forgetInstance('mailer');

        // Force reload the mail configuration
        $this->loadMailConfigFromDatabase();

        if ($this->option('debug')) {
            $this->line('🔄 Mail config reloaded');
        }
    }

    /**
     * Load mail configuration from database
     */
    private function loadMailConfigFromDatabase()
    {
        try {
            // Load mail configuration from database settings (same as MailConfigServiceProvider)
            $mailConfig = [
                'driver'     => Setting::get('mail_mailer', config('mail.mailer')),
                'host'       => Setting::get('mail_host', config('mail.mailers.smtp.host')),
                'port'       => Setting::get('mail_port', config('mail.mailers.smtp.port')) ?: 587, // Default to 587 if null
                'username'   => Setting::get('mail_username', config('mail.mailers.smtp.username')),
                'password'   => Setting::get('mail_password', config('mail.mailers.smtp.password')),
                'encryption' => Setting::get('mail_encryption', config('mail.mailers.smtp.encryption')),
                'from_address' => Setting::get('mail_from_address', config('mail.from.address')),
                'from_name'    => Setting::get('mail_from_name', config('mail.from.name')),
            ];

            // Set the configuration at runtime
            Config::set('mail.default', $mailConfig['driver']); // This is the key fix!
            Config::set('mail.mailer', $mailConfig['driver']);
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $mailConfig['host']);
            Config::set('mail.mailers.smtp.port', (int)$mailConfig['port']);
            Config::set('mail.mailers.smtp.username', $mailConfig['username']);
            Config::set('mail.mailers.smtp.password', $mailConfig['password']);
            Config::set('mail.mailers.smtp.encryption', $mailConfig['encryption']);
            Config::set('mail.from.address', $mailConfig['from_address']);
            Config::set('mail.from.name', $mailConfig['from_name']);

            $this->line('✅ Đã load cấu hình mail từ database');

        } catch (\Exception $e) {
            $this->warn('⚠️  Không thể load cấu hình mail từ database, sử dụng cấu hình mặc định');
            $this->line('   Lỗi: ' . $e->getMessage());
        }
    }

    /**
     * Get test email content
     */
    private function getTestEmailContent()
    {
        return "
🎉 Chúc mừng! SMTP của bạn đã hoạt động!

📧 Email này được gửi từ hệ thống " . config('app.name') . "
🕐 Thời gian: " . now()->format('d/m/Y H:i:s') . "
🌐 URL: " . config('app.url') . "

✅ Cấu hình SMTP đang hoạt động bình thường.

---
Đây là email test tự động, bạn không cần phản hồi.
        ";
    }
}
