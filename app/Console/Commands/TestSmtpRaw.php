<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class TestSmtpRaw extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'smtp:test-raw {email : Email to send to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test SMTP connection with raw PHP (detailed debugging)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        // Get SMTP config from database
        $host = Setting::get('mail_host', 'smtp.gmail.com');
        $port = Setting::get('mail_port', 587);
        $username = Setting::get('mail_username');
        $password = Setting::get('mail_password');
        $encryption = Setting::get('mail_encryption', 'tls');

        $this->info("🔧 Testing SMTP với raw PHP...");
        $this->line("Host: {$host}");
        $this->line("Port: {$port}");
        $this->line("Username: {$username}");
        $this->line("Encryption: {$encryption}");
        $this->newLine();

        // Test connection step by step
        $this->testRawSmtp($host, $port, $username, $password, $email, $encryption);
    }

    private function testRawSmtp($host, $port, $username, $password, $to, $encryption)
    {
        $this->line("🔌 Step 1: Testing connection to {$host}:{$port}");

        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true,
            ]
        ]);

        $socket = @stream_socket_client(
            "tcp://{$host}:{$port}",
            $errno,
            $errstr,
            30,
            STREAM_CLIENT_CONNECT,
            $context
        );

        if (!$socket) {
            $this->error("❌ Connection failed: {$errstr} ({$errno})");
            return;
        }

        $this->info("✅ Connected successfully");

        // Read server greeting
        $response = fgets($socket);
        $this->line("Server: " . trim($response));

        // Send EHLO
        $this->line("🤝 Step 2: EHLO handshake");
        fwrite($socket, "EHLO localhost\r\n");
        $response = $this->readMultilineResponse($socket);
        $this->line("EHLO Response: " . $response);

        // Start TLS if needed
        if ($encryption === 'tls') {
            $this->line("🔒 Step 3: Starting TLS");
            fwrite($socket, "STARTTLS\r\n");
            $response = fgets($socket);
            $this->line("STARTTLS: " . trim($response));

            if (strpos($response, '220') === 0) {
                if (stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    $this->info("✅ TLS enabled successfully");

                    // Send EHLO again after TLS
                    fwrite($socket, "EHLO localhost\r\n");
                    $response = $this->readMultilineResponse($socket);
                    $this->line("EHLO after TLS: " . $response);
                } else {
                    $this->error("❌ TLS failed");
                    fclose($socket);
                    return;
                }
            }
        }

        // Authenticate
        $this->line("🔑 Step 4: Authentication");
        fwrite($socket, "AUTH LOGIN\r\n");
        $response = fgets($socket);
        $this->line("AUTH LOGIN: " . trim($response));

        if (strpos($response, '334') === 0) {
            // Send username
            fwrite($socket, base64_encode($username) . "\r\n");
            $response = fgets($socket);
            $this->line("Username: " . trim($response));

            // Send password
            fwrite($socket, base64_encode($password) . "\r\n");
            $response = fgets($socket);
            $this->line("Password: " . trim($response));

            if (strpos($response, '235') === 0) {
                $this->info("✅ Authentication successful");

                // Try to send a test email
                $this->line("📧 Step 5: Sending test email");
                $this->sendTestEmail($socket, $username, $to);

            } else {
                $this->error("❌ Authentication failed: " . trim($response));
            }
        }

        fclose($socket);
    }

    private function readMultilineResponse($socket)
    {
        $response = '';
        while (($line = fgets($socket)) !== false) {
            $response .= $line;
            if (isset($line[3]) && $line[3] === ' ') {
                break;
            }
        }
        return trim($response);
    }

    private function sendTestEmail($socket, $from, $to)
    {
        // MAIL FROM
        fwrite($socket, "MAIL FROM:<{$from}>\r\n");
        $response = fgets($socket);
        $this->line("MAIL FROM: " . trim($response));

        // RCPT TO
        fwrite($socket, "RCPT TO:<{$to}>\r\n");
        $response = fgets($socket);
        $this->line("RCPT TO: " . trim($response));

        // DATA
        fwrite($socket, "DATA\r\n");
        $response = fgets($socket);
        $this->line("DATA: " . trim($response));

        if (strpos($response, '354') === 0) {
            // Send email content
            $content = "From: {$from}\r\n";
            $content .= "To: {$to}\r\n";
            $content .= "Subject: Raw SMTP Test\r\n";
            $content .= "\r\n";
            $content .= "This is a test email sent via raw SMTP.\r\n";
            $content .= "Time: " . date('Y-m-d H:i:s') . "\r\n";
            $content .= ".\r\n";

            fwrite($socket, $content);
            $response = fgets($socket);
            $this->line("Send result: " . trim($response));

            if (strpos($response, '250') === 0) {
                $this->info("✅ Email sent successfully!");
            } else {
                $this->error("❌ Email send failed: " . trim($response));
            }
        }

        // QUIT
        fwrite($socket, "QUIT\r\n");
        $response = fgets($socket);
        $this->line("QUIT: " . trim($response));
    }
}
