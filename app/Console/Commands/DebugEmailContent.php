<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DebugEmailContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:debug-content {log_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug email content to check if tracking is added';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $logId = $this->argument('log_id');

        $emailLog = \App\Models\EmailLog::with(['workflow.emailTemplate', 'participant', 'campaign.webinar'])->find($logId);

        if (!$emailLog) {
            $this->error("Email log with ID {$logId} not found.");
            return 1;
        }

        $this->info("🔍 Debugging Email Content for Log ID: {$logId}");
        $this->line("Email: {$emailLog->recipient_email}");
        $this->line("Status: {$emailLog->status}");
        $this->newLine();

        // Get template and process it
        $template = $emailLog->workflow->emailTemplate;
        $participant = $emailLog->participant;
        $webinar = $emailLog->campaign->webinar;

        // Prepare variables
        $variables = [
            '{name}' => $participant->name ?? 'Test User',
            '{webinar_title}' => $webinar->title ?? 'Demo Webinar',
            '{speaker}' => $webinar->speaker ?? 'Demo Speaker',
            '{join_url}' => $webinar ? route('join.show', $webinar->join_code) : 'https://example.com/join',
        ];

        // Process template
        $processedTemplate = $template->replaceVariables($variables);

        $this->info("📧 Original Template Content:");
        $this->line("Subject: " . $processedTemplate['subject']);
        $this->newLine();
        $this->line("Content (first 200 chars):");
        $this->line(substr($processedTemplate['content'], 0, 200) . '...');
        $this->newLine();

        // Add tracking
        $trackedContent = $this->addEmailTracking($processedTemplate['content'], $emailLog);

        $this->info("🔗 Content with Tracking:");
        $this->line("Content length: " . strlen($trackedContent) . " chars");
        $this->newLine();

        // Check for tracking pixel
        if (strpos($trackedContent, 'email-tracking/open') !== false) {
            $this->info("✅ Tracking pixel found!");
        } else {
            $this->error("❌ Tracking pixel NOT found!");
        }

        // Check for tracking links
        $linkCount = preg_match_all('/email-tracking\/click/', $trackedContent);
        $this->line("🔗 Tracking links found: {$linkCount}");

        // Show tracking URLs
        $this->newLine();
        $this->info("🌐 Tracking URLs:");
        $this->line("Open: " . route('email-marketing.track.open', ['log' => $logId]));
        $this->line("Click: " . route('email-marketing.track.click', ['log' => $logId, 'url' => base64_encode('https://example.com')]));

        // Show last 500 chars to see tracking pixel
        $this->newLine();
        $this->info("📄 Last 500 characters (should contain tracking pixel):");
        $this->line(substr($trackedContent, -500));

        return 0;
    }

    /**
     * Add tracking pixel and convert links to tracking links
     */
    private function addEmailTracking($content, $emailLog)
    {
        // Add tracking pixel at the end of email
        $trackingPixelUrl = route('email-marketing.track.open', ['log' => $emailLog->id]);
        $trackingPixel = '<img src="' . $trackingPixelUrl . '" width="1" height="1" style="display:none;" alt="">';

        // Convert all links to tracking links
        $content = preg_replace_callback(
            '/<a\s+(?:[^>]*?\s+)?href=(["\'])(.*?)\1/i',
            function ($matches) use ($emailLog) {
                $originalUrl = $matches[2];

                // Skip if it's already a tracking URL or email/tel links
                if (strpos($originalUrl, 'email-tracking') !== false ||
                    strpos($originalUrl, 'mailto:') === 0 ||
                    strpos($originalUrl, 'tel:') === 0 ||
                    strpos($originalUrl, '#') === 0) {
                    return $matches[0];
                }

                // Create tracking URL
                $trackingUrl = route('email-marketing.track.click', [
                    'log' => $emailLog->id,
                    'url' => base64_encode($originalUrl)
                ]);

                return str_replace($matches[2], $trackingUrl, $matches[0]);
            },
            $content
        );

        // Add tracking pixel before closing body tag, or at the end if no body tag
        if (strpos($content, '</body>') !== false) {
            $content = str_replace('</body>', $trackingPixel . '</body>', $content);
        } else {
            $content .= $trackingPixel;
        }

        return $content;
    }
}
