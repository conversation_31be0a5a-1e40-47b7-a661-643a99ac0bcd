<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;

class TestTrackingURL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-tracking-url {log_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test tracking URL by simulating HTTP request';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $logId = $this->argument('log_id');

        $emailLog = \App\Models\EmailLog::find($logId);

        if (!$emailLog) {
            $this->error("Email log with ID {$logId} not found.");
            return 1;
        }

        $this->info("Testing tracking for Email Log ID: {$logId}");
        $this->line("Email: {$emailLog->recipient_email}");
        $this->line("Current Status: {$emailLog->status}");
        $this->line("Current Opens: {$emailLog->open_count}");
        $this->line("Current Clicks: {$emailLog->click_count}");
        $this->newLine();

        // Test tracking URLs
        $openUrl = route('email-marketing.track.open', ['log' => $logId]);
        $clickUrl = route('email-marketing.track.click', ['log' => $logId, 'url' => base64_encode('https://example.com')]);

        $this->info("🔗 Tracking URLs:");
        $this->line("Open: {$openUrl}");
        $this->line("Click: {$clickUrl}");
        $this->newLine();

        // Simulate tracking by calling controller methods directly
        $this->info("🔍 Simulating tracking...");

        try {
            // Create mock request
            $request = new \Illuminate\Http\Request();
            $request->server->set('HTTP_USER_AGENT', 'Test User Agent');
            $request->server->set('REMOTE_ADDR', '127.0.0.1');

            // Test open tracking
            $controller = new \App\Http\Controllers\EmailTrackingController();
            $response = $controller->trackOpen($request, $logId);

            if ($response->getStatusCode() === 200) {
                $this->info("✅ Open tracking successful!");
            } else {
                $this->error("❌ Open tracking failed!");
            }

            // Test click tracking
            $request->query->set('url', base64_encode('https://example.com'));
            $response = $controller->trackClick($request, $logId);

            if ($response->getStatusCode() === 302) {
                $this->info("✅ Click tracking successful!");
            } else {
                $this->error("❌ Click tracking failed!");
            }

            // Check updated stats
            $emailLog->refresh();
            $this->newLine();
            $this->info("📊 Updated Stats:");
            $this->line("Status: {$emailLog->status}");
            $this->line("Opens: {$emailLog->open_count}");
            $this->line("Clicks: {$emailLog->click_count}");
            $this->line("Opened At: " . ($emailLog->opened_at ? $emailLog->opened_at->format('Y-m-d H:i:s') : 'Not opened'));
            $this->line("Clicked At: " . ($emailLog->clicked_at ? $emailLog->clicked_at->format('Y-m-d H:i:s') : 'Not clicked'));

        } catch (\Exception $e) {
            $this->error("Error testing tracking: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
