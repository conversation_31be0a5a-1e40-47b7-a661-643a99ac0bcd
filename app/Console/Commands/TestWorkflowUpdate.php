<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailWorkflow;
use App\Models\EmailLog;
use Carbon\Carbon;

class TestWorkflowUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:workflow-update {workflow_id} {--new-time=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test workflow update functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $workflowId = $this->argument('workflow_id');
        $newTime = $this->option('new-time') ?: '15:00';

        $workflow = EmailWorkflow::find($workflowId);

        if (!$workflow) {
            $this->error("Workflow {$workflowId} not found");
            return 1;
        }

        $this->info("🔍 Testing workflow update for ID: {$workflowId}");
        $this->info("Current schedule: {$workflow->scheduled_at}");

        // Show current email logs
        $currentLogs = EmailLog::where('workflow_id', $workflowId)->get();
        $this->info("📧 Current email logs: {$currentLogs->count()}");

        foreach ($currentLogs as $log) {
            $this->line("  - ID: {$log->id} | Email: {$log->recipient_email} | Status: {$log->status} | Scheduled: {$log->scheduled_at}");
        }

        // Update workflow schedule
        $newSchedule = Carbon::today()->addHours(15); // Today at 3 PM
        if ($newTime) {
            $timeParts = explode(':', $newTime);
            $hours = (int) $timeParts[0];
            $minutes = (int) ($timeParts[1] ?? 0);
            $newSchedule = Carbon::today()->addHours($hours)->addMinutes($minutes);
        }

        $this->info("🔄 Updating workflow schedule to: {$newSchedule}");

        // Simulate the update
        $workflow->update(['scheduled_at' => $newSchedule]);

        // Call the handle method manually
        $controller = new \App\Http\Controllers\EmailMarketingController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('handleWorkflowScheduleUpdate');
        $method->setAccessible(true);
        $method->invoke($controller, $workflow);

        // Show updated email logs
        $updatedLogs = EmailLog::where('workflow_id', $workflowId)->get();
        $this->info("📧 Updated email logs: {$updatedLogs->count()}");

        foreach ($updatedLogs as $log) {
            $this->line("  - ID: {$log->id} | Email: {$log->recipient_email} | Status: {$log->status} | Scheduled: {$log->scheduled_at}");
        }

        $this->info("✅ Workflow update test completed!");

        return 0;
    }
}
