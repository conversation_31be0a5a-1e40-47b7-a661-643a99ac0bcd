<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailCampaign;
use App\Models\EmailWorkflow;
use App\Models\EmailLog;
use App\Models\WebinarParticipant;
use Carbon\Carbon;

class ProcessEmailCampaigns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:process-campaigns {--dry-run : Show what would be processed without actually sending}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process email campaigns and send scheduled emails';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Processing Email Campaigns...');

        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No emails will be sent');
        }

        // 1. Process active campaigns
        $this->processActiveCampaigns($dryRun);

        // 2. Process scheduled workflows
        $this->processScheduledWorkflows($dryRun);

        // 3. Send pending emails
        $this->sendPendingEmails($dryRun);

        $this->info('✅ Email campaign processing completed!');
    }

    /**
     * Process active campaigns to create email logs for new participants
     */
    protected function processActiveCampaigns($dryRun = false)
    {
        $this->info('📋 Processing active campaigns...');

        $activeCampaigns = EmailCampaign::where('status', 'active')
            ->where('is_active', true)
            ->with(['workflows', 'webinar'])
            ->get();

        foreach ($activeCampaigns as $campaign) {
            $this->line("  Processing campaign: {$campaign->name}");

            // Get participants for this campaign
            $participants = $this->getCampaignParticipants($campaign);

            foreach ($participants as $participant) {
                // Check if participant already has email logs for this campaign
                $existingLogs = EmailLog::where('campaign_id', $campaign->id)
                    ->where('webinar_participant_id', $participant->id)
                    ->exists();

                if (!$existingLogs) {
                    // Create email logs for all workflows in this campaign
                    foreach ($campaign->workflows as $workflow) {
                        if ($workflow->is_active) {
                            $this->createEmailLog($workflow, $participant, $dryRun);
                        }
                    }
                }
            }
        }
    }

    /**
     * Process workflows that are scheduled to run now
     */
    protected function processScheduledWorkflows($dryRun = false)
    {
        $this->info('⏰ Processing scheduled workflows...');

        $now = Carbon::now();

        $scheduledWorkflows = EmailWorkflow::whereNotNull('scheduled_at')
            ->where('scheduled_at', '<=', $now)
            ->where('is_active', true)
            ->whereHas('campaign', function($query) {
                $query->where('status', 'active')->where('is_active', true);
            })
            ->with(['campaign.webinar', 'emailTemplate'])
            ->get();

        foreach ($scheduledWorkflows as $workflow) {
            $this->line("  Processing scheduled workflow: {$workflow->emailTemplate->subject}");

            // Get participants for this workflow's campaign
            $participants = $this->getCampaignParticipants($workflow->campaign);

            foreach ($participants as $participant) {
                // Check if email log already exists for this workflow and participant
                $existingLog = EmailLog::where('workflow_id', $workflow->id)
                    ->where('webinar_participant_id', $participant->id)
                    ->first();

                if (!$existingLog) {
                    $this->createEmailLog($workflow, $participant, $dryRun);
                }
            }
        }
    }

    /**
     * Send pending emails that are ready to be sent
     */
    protected function sendPendingEmails($dryRun = false)
    {
        $this->info('📧 Sending pending emails...');

        $now = Carbon::now();

        $pendingEmails = EmailLog::where('status', 'pending')
            ->where(function($query) use ($now) {
                $query->whereNull('scheduled_at')
                      ->orWhere('scheduled_at', '<=', $now);
            })
            ->with(['campaign', 'workflow', 'participant'])
            ->limit(100) // Process in batches
            ->get();

        foreach ($pendingEmails as $emailLog) {
            if ($dryRun) {
                $this->line("  [DRY RUN] Would send email to: {$emailLog->recipient_email}");
            } else {
                $this->sendEmail($emailLog);
            }
        }

        $this->info("  Processed {$pendingEmails->count()} pending emails");
    }

    /**
     * Get participants for a campaign
     */
    protected function getCampaignParticipants($campaign)
    {
        if ($campaign->webinar_id) {
            return WebinarParticipant::where('webinar_id', $campaign->webinar_id)->get();
        }

        // If no specific webinar, get participants from all user's webinars
        $webinarIds = $campaign->user->webinars()->pluck('id');
        return WebinarParticipant::whereIn('webinar_id', $webinarIds)->get();
    }

    /**
     * Create email log for a workflow and participant
     */
    protected function createEmailLog($workflow, $participant, $dryRun = false)
    {
        if ($dryRun) {
            $this->line("    [DRY RUN] Would create email log for: {$participant->email}");
            return;
        }

        $scheduledAt = $workflow->scheduled_at ?: now();

        $emailLog = $workflow->createEmailLog($participant, $scheduledAt);

        $this->line("    Created email log for: {$participant->email}");
    }

    /**
     * Send an email using Laravel Mail
     */
    protected function sendEmail($emailLog)
    {
        try {
            // Load mail configuration from database
            $this->loadMailConfig();

            // Get template and process variables
            $template = $emailLog->workflow->emailTemplate;
            $participant = $emailLog->participant;
            $webinar = $emailLog->campaign->webinar;

            // Prepare variables for template
            $variables = [
                'name' => $participant->name ?? 'Bạn',
                'email' => $participant->email ?? '',
                'phone' => $participant->phone ?? '',
                'webinar_title' => $webinar->title ?? '',
                'webinar_speaker' => $webinar->speaker ?? '',
                'join_url' => $webinar ? route('join.show', $webinar->join_code) : '#',
                'join_code' => $webinar->join_code ?? '',
            ];

            // Replace variables in template
            $processedTemplate = $template->replaceVariables($variables);

            // Add tracking pixel and process links
            $trackedContent = $this->addEmailTracking($processedTemplate['content'], $emailLog);

            // Send email using Laravel Mail
            \Mail::send([], [], function ($message) use ($emailLog, $processedTemplate, $trackedContent) {
                $message->to($emailLog->recipient_email, $emailLog->recipient_name)
                        ->subject($processedTemplate['subject'])
                        ->html($trackedContent)
                        ->from(config('mail.mailers.smtp.username'), config('mail.from.name'));
            });

            $emailLog->markAsSent();
            $this->line("  ✅ Sent email to: {$emailLog->recipient_email}");

        } catch (\Exception $e) {
            $emailLog->markAsFailed($e->getMessage());
            $this->error("  ❌ Failed to send email to: {$emailLog->recipient_email} - {$e->getMessage()}");
        }
    }

    /**
     * Load mail configuration from database
     */
    protected function loadMailConfig()
    {
        try {
            $mailConfig = [
                'driver'     => \App\Models\Setting::get('mail_mailer', 'smtp'),
                'host'       => \App\Models\Setting::get('mail_host', 'smtp.gmail.com'),
                'port'       => \App\Models\Setting::get('mail_port', 587),
                'username'   => \App\Models\Setting::get('mail_username'),
                'password'   => \App\Models\Setting::get('mail_password'),
                'encryption' => \App\Models\Setting::get('mail_encryption', 'tls'),
                'from_address' => \App\Models\Setting::get('mail_from_address'),
                'from_name'    => \App\Models\Setting::get('mail_from_name'),
            ];

            // Set the configuration at runtime
            \Config::set('mail.default', $mailConfig['driver']);
            \Config::set('mail.mailer', $mailConfig['driver']);
            \Config::set('mail.mailers.smtp.transport', 'smtp');
            \Config::set('mail.mailers.smtp.host', $mailConfig['host']);
            \Config::set('mail.mailers.smtp.port', (int)($mailConfig['port'] ?: 587));
            \Config::set('mail.mailers.smtp.username', $mailConfig['username']);
            \Config::set('mail.mailers.smtp.password', $mailConfig['password']);
            \Config::set('mail.mailers.smtp.encryption', $mailConfig['encryption']);
            \Config::set('mail.from.address', $mailConfig['from_address']);
            \Config::set('mail.from.name', $mailConfig['from_name']);

        } catch (\Exception $e) {
            $this->warn('⚠️  Could not load mail config from database: ' . $e->getMessage());
        }
    }

    /**
     * Add tracking pixel and convert links to tracking links
     */
    protected function addEmailTracking($content, $emailLog)
    {
        // Add tracking pixel at the end of email
        $trackingPixelUrl = route('email-marketing.track.open', ['log' => $emailLog->id]);
        $trackingPixel = '<img src="' . $trackingPixelUrl . '" width="1" height="1" style="display:none;" alt="">';

        // Convert all links to tracking links
        $content = preg_replace_callback(
            '/<a\s+(?:[^>]*?\s+)?href=(["\'])(.*?)\1/i',
            function ($matches) use ($emailLog) {
                $originalUrl = $matches[2];

                // Skip if it's already a tracking URL or email/tel links
                if (strpos($originalUrl, 'email-tracking') !== false ||
                    strpos($originalUrl, 'mailto:') === 0 ||
                    strpos($originalUrl, 'tel:') === 0 ||
                    strpos($originalUrl, '#') === 0) {
                    return $matches[0];
                }

                // Create tracking URL
                $trackingUrl = route('email-marketing.track.click', [
                    'log' => $emailLog->id,
                    'url' => base64_encode($originalUrl)
                ]);

                return str_replace($matches[2], $trackingUrl, $matches[0]);
            },
            $content
        );

        // Add tracking pixel before closing body tag, or at the end if no body tag
        if (strpos($content, '</body>') !== false) {
            $content = str_replace('</body>', $trackingPixel . '</body>', $content);
        } else {
            $content .= $trackingPixel;
        }

        return $content;
    }
}
