<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailWorkflow;
use App\Models\EmailLog;

class CheckWorkflowStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:check-workflow-stats {workflow_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check workflow statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $workflowId = $this->argument('workflow_id');

        if (!$workflowId) {
            // List all workflows
            $workflows = EmailWorkflow::all();
            $this->info("📋 Available Workflows:");
            foreach ($workflows as $workflow) {
                $this->line("  ID: {$workflow->id} | Sequence: {$workflow->sequence_order} | Active: " . ($workflow->is_active ? 'Yes' : 'No'));
            }
            return 0;
        }

        $workflow = EmailWorkflow::find($workflowId);

        if (!$workflow) {
            $this->error("Workflow {$workflowId} not found");
            return 1;
        }

        $this->info("🔄 Workflow {$workflowId} Statistics:");

        // Get all email logs for this workflow
        $logs = EmailLog::where('workflow_id', $workflowId)->get();

        $this->info("📧 Email Logs ({$logs->count()} total):");
        foreach ($logs as $log) {
            $this->line("  ID: {$log->id} | Email: {$log->recipient_email} | Status: {$log->status} | Opens: {$log->open_count} | Clicks: {$log->click_count}");
        }

        $this->newLine();
        $this->info("📊 Current Logic Statistics:");

        // Current logic (wrong)
        $sentCount = $logs->where('status', 'sent')->count();
        $openedCount = $logs->where('status', 'opened')->count();
        $clickedCount = $logs->where('status', 'clicked')->count();

        $this->line("  Sent (status='sent'): {$sentCount}");
        $this->line("  Opened (status='opened'): {$openedCount}");
        $this->line("  Clicked (status='clicked'): {$clickedCount}");

        $this->newLine();
        $this->info("📊 Correct Logic Statistics:");

        // Correct logic
        $actualSent = $logs->whereIn('status', ['sent', 'opened', 'clicked'])->count();
        $actualOpened = $logs->where('open_count', '>', 0)->count();
        $actualClicked = $logs->where('click_count', '>', 0)->count();

        $this->line("  Actually Sent: {$actualSent}");
        $this->line("  Actually Opened: {$actualOpened}");
        $this->line("  Actually Clicked: {$actualClicked}");

        return 0;
    }
}
