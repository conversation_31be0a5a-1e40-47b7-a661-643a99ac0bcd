<?php

namespace App\Models;

use Hashids\Hashids;
use App\Models\Advertisement;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Webinar extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'speaker',
        'virtual_viewers',
        'virtual_viewer_names',
        'only_show_my_comment',
        'total_view',
        'waiting_time',
        'join_settings',
        'notification_settings',
        'join_code',
        'join_url',
        'video_path',
        'pin_comment',
        's3_url',
        'schedules',
        'seeded_comments',
        'advertisement_slots',
        'video_duration_minutes',
        'video_file_size',
        'allow_replay',
        'livestreams',
        'livestream_learning',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'schedules' => 'array',
        'join_settings' => 'array',
        'notification_settings' => 'array',
        'seeded_comments' => 'array',
        'advertisement_slots' => 'array',
        'livestreams' => 'array',
        'livestream_learning' => 'array',
        'allow_replay' => 'boolean',
    ];

    /**
     * Get hashids instance
     *
     * @return \Hashids\Hashids
     */
    protected function getHashids()
    {
        $salt = config('app.key', env('APP_KEY'));
        $minHashLength = 16;
        return new Hashids($salt, $minHashLength);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    /**
     * Get the route key value for the model.
     *
     * @return string
     */
    public function getRouteKey()
    {
        return $this->hashId();
    }

    /**
     * Retrieve the model for a bound value.
     *
     * @param mixed $value
     * @param string|null $field
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function resolveRouteBinding($value, $field = null)
    {
        $id = self::decodeHashId($value);

        if (!$id) {
            return null;
        }

        return $this->where('id', $id)->first();
    }

    /**
     * Convert the model's ID to a hashed ID.
     *
     * @return string
     */
    public function hashId()
    {
        return $this->getHashids()->encode($this->id);
    }

    /**
     * Decode a hashed ID back to the original ID.
     *
     * @param string $hashId
     * @return int|null
     */
    public static function decodeHashId($hashId)
    {
        try {
            // Skip if hashId is empty
            if (empty($hashId)) {
                return null;
            }

            $decoded = (new static)->getHashids()->decode($hashId);

            // Hashids returns an array of numbers, we need the first one
            if (!empty($decoded)) {
                return $decoded[0];
            }

            return null;
        } catch (\Exception $e) {
            // Log error for debugging if needed
            // \Log::error('Hash decode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the user that owns the webinar.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Generate a unique join code.
     */
    public static function generateJoinCode()
    {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $code = '';

        do {
            $code = '';
            for ($i = 0; $i < 8; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (self::where('join_code', $code)->exists());

        return $code;
    }

    /**
     * Get the participants for the webinar.
     */
    public function participants()
    {
        return $this->hasMany(WebinarParticipant::class);
    }
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the comments for the webinar.
     */
    public function comments()
    {
        return $this->hasMany(WebinarComment::class);
    }

    public function file_comments()
    {
        return $this->hasMany(FileComment::class);
    }

    /**
     * Get the smart link schedules for the webinar.
     */
    public function smartLinkSchedules()
    {
        return $this->hasMany(SmartLinkSchedule::class);
    }

    /**
     * Get the question sets for the webinar.
     */
    public function questionSets()
    {
        return $this->hasMany(QuestionSet::class);
    }

    /**
     * Get the questions for the webinar.
     */
    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    /**
     * Get the advertisements related to this webinar.
     */
    public function advertisements()
    {
        // Vì advertisement_slots là một JSON array trong webinar, ta cần phải sử dụng
        // hasManyThrough hoặc truy vấn trực tiếp thông qua Advertisement model

        // Trong trường hợp này, chúng ta tạo một custom collection thay vì relationship
        // để tránh lỗi khi eager loading
        return Advertisement::when($this->exists, function ($query) {
            $adIds = collect($this->advertisement_slots ?? [])
                ->pluck('advertisement_id')
                ->filter()
                ->toArray();

            return $query->whereIn('id', $adIds);
        });
    }

    /**
     * Load advertisement data for this webinar
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAdvertisementsAttribute()
    {
        if (!$this->exists) {
            return collect();
        }

        $adIds = collect($this->advertisement_slots ?? [])
            ->pluck('advertisement_id')
            ->filter()
            ->toArray();

        return Advertisement::whereIn('id', $adIds)->get();
    }

    /**
     * Get the default join settings.
     */
    public function getDefaultJoinSettings()
    {
        return [
            'name_required' => true,
            'phone_required' => true,
            'email_required' => false,
        ];
    }

    /**
     * Get the join settings with defaults applied.
     */
    public function getJoinSettingsAttribute($value)
    {
        $settings = json_decode($value, true) ?: [];
        return array_merge($this->getDefaultJoinSettings(), $settings);
    }

    /**
     * Get the default notification settings.
     */
    public function getDefaultNotificationSettings()
    {
        return [
            'telegram_notify_participants' => '0',
            'telegram_notify_comments' => '0',
        ];
    }

    /**
     * Get the notification settings with defaults applied.
     */
    public function getNotificationSettingsAttribute($value)
    {
        $settings = json_decode($value, true) ?: [];
        return array_merge($this->getDefaultNotificationSettings(), $settings);
    }
}
