<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailWorkflow extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'campaign_id',
        'email_template_id',
        'sequence_order',
        'delay_days',
        'delay_hours',
        'delay_minutes',
        'scheduled_at',
        'conditions',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'conditions' => 'array',
        'scheduled_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the campaign that owns the workflow.
     */
    public function campaign()
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    /**
     * Get the email template for this workflow.
     */
    public function emailTemplate()
    {
        return $this->belongsTo(EmailTemplate::class, 'email_template_id');
    }

    /**
     * Get the email logs for this workflow.
     */
    public function emailLogs()
    {
        return $this->hasMany(EmailLog::class, 'workflow_id');
    }

    /**
     * Scope for active workflows.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Calculate the scheduled time for sending email.
     */
    public function calculateScheduledTime($triggerTime)
    {
        $scheduledTime = Carbon::parse($triggerTime);

        if ($this->delay_days > 0) {
            $scheduledTime->addDays($this->delay_days);
        }

        if ($this->delay_hours > 0) {
            $scheduledTime->addHours($this->delay_hours);
        }

        if ($this->delay_minutes > 0) {
            $scheduledTime->addMinutes($this->delay_minutes);
        }

        return $scheduledTime;
    }

    /**
     * Check if conditions are met for sending email.
     */
    public function checkConditions($participant, $webinar = null)
    {
        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $condition) {
            $field = $condition['field'] ?? null;
            $operator = $condition['operator'] ?? 'equals';
            $value = $condition['value'] ?? null;

            if (!$this->evaluateCondition($participant, $webinar, $field, $operator, $value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     */
    protected function evaluateCondition($participant, $webinar, $field, $operator, $value)
    {
        $fieldValue = $this->getFieldValue($participant, $webinar, $field);

        switch ($operator) {
            case 'equals':
                return $fieldValue == $value;
            case 'not_equals':
                return $fieldValue != $value;
            case 'contains':
                return strpos(strtolower($fieldValue), strtolower($value)) !== false;
            case 'not_contains':
                return strpos(strtolower($fieldValue), strtolower($value)) === false;
            case 'greater_than':
                return $fieldValue > $value;
            case 'less_than':
                return $fieldValue < $value;
            case 'is_empty':
                return empty($fieldValue);
            case 'is_not_empty':
                return !empty($fieldValue);
            default:
                return true;
        }
    }

    /**
     * Get field value from participant or webinar.
     */
    protected function getFieldValue($participant, $webinar, $field)
    {
        switch ($field) {
            case 'participant_name':
                return $participant->name ?? '';
            case 'participant_email':
                return $participant->email ?? '';
            case 'participant_phone':
                return $participant->phone ?? '';
            case 'participant_join_count':
                return $participant->join_count ?? 0;
            case 'webinar_title':
                return $webinar->title ?? '';
            case 'webinar_speaker':
                return $webinar->speaker ?? '';
            case 'days_since_registration':
                return $participant->created_at ? now()->diffInDays($participant->created_at) : 0;
            default:
                return '';
        }
    }

    /**
     * Get total delay in minutes.
     */
    public function getTotalDelayMinutes()
    {
        return ($this->delay_days * 24 * 60) + ($this->delay_hours * 60) + $this->delay_minutes;
    }

    /**
     * Get delay description.
     */
    public function getDelayDescription()
    {
        $parts = [];

        if ($this->delay_days > 0) {
            $parts[] = $this->delay_days . ' ngày';
        }

        if ($this->delay_hours > 0) {
            $parts[] = $this->delay_hours . ' giờ';
        }

        if ($this->delay_minutes > 0) {
            $parts[] = $this->delay_minutes . ' phút';
        }

        if (empty($parts)) {
            return 'Ngay lập tức';
        }

        return implode(', ', $parts);
    }

    /**
     * Check if this workflow should be executed.
     */
    public function shouldExecute($participant, $webinar = null)
    {
        if (!$this->is_active) {
            return false;
        }

        if (!$this->campaign->isActive()) {
            return false;
        }

        return $this->checkConditions($participant, $webinar);
    }

    /**
     * Create email log for this workflow.
     */
    public function createEmailLog($participant, $scheduledAt = null)
    {
        $template = $this->emailTemplate;
        $webinar = $participant->webinar;

        // Prepare variables for template
        $variables = [
            'name' => $participant->name ?? 'Bạn',
            'email' => $participant->email ?? '',
            'phone' => $participant->phone ?? '',
            'webinar_title' => $webinar->title ?? '',
            'webinar_speaker' => $webinar->speaker ?? '',
            'join_url' => route('join.show', $webinar->join_code ?? ''),
            'join_code' => $webinar->join_code ?? '',
        ];

        // Add webinar schedule info if available
        if ($webinar->schedules) {
            $schedules = is_array($webinar->schedules) ? $webinar->schedules : json_decode($webinar->schedules, true);
            $nextSchedule = collect($schedules)->first();
            if ($nextSchedule) {
                $variables['webinar_date'] = Carbon::parse($nextSchedule['date'])->format('d/m/Y');
                $variables['webinar_time'] = $nextSchedule['time'];
            }
        }

        // Replace variables in template
        $processedTemplate = $template->replaceVariables($variables);

        return EmailLog::create([
            'campaign_id' => $this->campaign_id,
            'workflow_id' => $this->id,
            'webinar_participant_id' => $participant->id,
            'recipient_email' => $participant->email,
            'recipient_name' => $participant->name,
            'subject' => $processedTemplate['subject'],
            'content' => $processedTemplate['content'],
            'status' => 'pending',
            'scheduled_at' => $scheduledAt ?: now(),
        ]);
    }

    /**
     * Get statistics for this workflow.
     */
    public function getStats()
    {
        $logs = $this->emailLogs();

        return [
            'total_scheduled' => $logs->count(),
            'total_sent' => $logs->whereIn('status', ['sent', 'opened', 'clicked'])->count(),
            'total_opened' => $logs->where('open_count', '>', 0)->count(),
            'total_clicked' => $logs->where('click_count', '>', 0)->count(),
            'total_failed' => $logs->where('status', 'failed')->count(),
        ];
    }
}
