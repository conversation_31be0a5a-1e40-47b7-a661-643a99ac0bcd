<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTemplate extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'campaign_id',
        'name',
        'subject',
        'content',
        'template_type',
        'design_json',
        'variables',
        'is_default',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'design_json' => 'array',
        'variables' => 'array',
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the template.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the campaign that owns the template.
     */
    public function campaign()
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    /**
     * Get the workflows that use this template.
     */
    public function workflows()
    {
        return $this->hasMany(EmailWorkflow::class, 'email_template_id');
    }

    /**
     * Scope for default templates.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for templates by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('template_type', $type);
    }

    /**
     * Replace variables in template content.
     */
    public function replaceVariables($variables = [])
    {
        $content = $this->content;
        $subject = $this->subject;

        foreach ($variables as $key => $value) {
            $placeholder = '{' . $key . '}';
            $content = str_replace($placeholder, $value, $content);
            $subject = str_replace($placeholder, $value, $subject);
        }

        return [
            'subject' => $subject,
            'content' => $content,
        ];
    }

    /**
     * Get available variables for this template.
     */
    public function getAvailableVariables()
    {
        return [
            'name' => 'Tên người đăng ký',
            'email' => 'Email người đăng ký',
            'phone' => 'Số điện thoại',
            'webinar_title' => 'Tên webinar',
            'webinar_speaker' => 'Diễn giả',
            'join_url' => 'Link tham gia webinar',
            'join_code' => 'Mã tham gia',
            'webinar_date' => 'Ngày webinar',
            'webinar_time' => 'Giờ webinar',
        ];
    }

    /**
     * Preview template with sample data.
     */
    public function preview()
    {
        $sampleData = [
            'name' => 'Nguyễn Văn A',
            'email' => '<EMAIL>',
            'phone' => '0123456789',
            'webinar_title' => 'Webinar Mẫu',
            'webinar_speaker' => 'Diễn giả mẫu',
            'join_url' => 'https://webinar.test/join/sample',
            'join_code' => 'SAMPLE123',
            'webinar_date' => '01/01/2024',
            'webinar_time' => '20:00',
        ];

        return $this->replaceVariables($sampleData);
    }
}
