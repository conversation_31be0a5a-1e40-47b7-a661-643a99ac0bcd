<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EmailLog extends Model
{
    protected $fillable = [
        'campaign_id',
        'workflow_id',
        'webinar_participant_id',
        'recipient_email',
        'recipient_name',
        'subject',
        'content',
        'status',
        'error_message',
        'scheduled_at',
        'sent_at',
        'delivered_at',
        'opened_at',
        'clicked_at',
        'open_count',
        'click_count',
        'tracking_data',
    ];

    protected $casts = [
        'tracking_data' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'opened_at' => 'datetime',
        'clicked_at' => 'datetime',
        'open_count' => 'integer',
        'click_count' => 'integer',
    ];

    public function markAsSent()
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    public function markAsOpened()
    {
        $this->update([
            'status' => 'opened',
            'opened_at' => $this->opened_at ?: now(),
            'open_count' => $this->open_count + 1,
        ]);
    }

    public function markAsClicked()
    {
        $this->update([
            'status' => 'clicked',
            'clicked_at' => $this->clicked_at ?: now(),
            'click_count' => $this->click_count + 1,
        ]);
    }

    // Relationships
    public function campaign()
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    public function workflow()
    {
        return $this->belongsTo(EmailWorkflow::class, 'workflow_id');
    }

    public function participant()
    {
        return $this->belongsTo(WebinarParticipant::class, 'webinar_participant_id');
    }

    // Helper methods
    public function getTrackingPixelUrl()
    {
        return route('email-marketing.track.open', ['log' => $this->id]);
    }

    public function getClickTrackingUrl($originalUrl)
    {
        return route('email-marketing.track.click', [
            'log' => $this->id,
            'url' => base64_encode($originalUrl)
        ]);
    }

    public function wasOpened()
    {
        return $this->open_count > 0;
    }

    public function wasClicked()
    {
        return $this->click_count > 0;
    }
}
