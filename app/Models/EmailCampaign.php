<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailCampaign extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'webinar_id',
        'name',
        'description',
        'status',
        'trigger_type',
        'trigger_conditions',
        'is_active',
        'started_at',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trigger_conditions' => 'array',
        'is_active' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the campaign.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the webinar associated with the campaign.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Get the email templates for the campaign.
     */
    public function emailTemplates()
    {
        return $this->hasMany(EmailTemplate::class, 'campaign_id');
    }

    /**
     * Get the workflows for the campaign.
     */
    public function workflows()
    {
        return $this->hasMany(EmailWorkflow::class, 'campaign_id')->orderBy('sequence_order');
    }

    /**
     * Get the email logs for the campaign.
     */
    public function emailLogs()
    {
        return $this->hasMany(EmailLog::class, 'campaign_id');
    }

    /**
     * Scope for active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')->where('is_active', true);
    }

    /**
     * Scope for campaigns by trigger type.
     */
    public function scopeByTriggerType($query, $triggerType)
    {
        return $query->where('trigger_type', $triggerType);
    }

    /**
     * Get campaign statistics.
     */
    public function getStatsAttribute()
    {
        $logs = $this->emailLogs();

        return [
            'total_sent' => $logs->whereIn('status', ['sent', 'opened', 'clicked'])->count(),
            'total_delivered' => $logs->where('status', 'delivered')->count(),
            'total_opened' => $logs->where('open_count', '>', 0)->count(),
            'total_clicked' => $logs->where('click_count', '>', 0)->count(),
            'total_failed' => $logs->where('status', 'failed')->count(),
            'open_rate' => $this->calculateOpenRate(),
            'click_rate' => $this->calculateClickRate(),
        ];
    }

    /**
     * Calculate open rate percentage.
     */
    public function calculateOpenRate()
    {
        $sent = $this->emailLogs()->whereIn('status', ['sent', 'opened', 'clicked'])->count();
        $opened = $this->emailLogs()->where('open_count', '>', 0)->count();

        return $sent > 0 ? round(($opened / $sent) * 100, 2) : 0;
    }

    /**
     * Calculate click rate percentage.
     */
    public function calculateClickRate()
    {
        $sent = $this->emailLogs()->whereIn('status', ['sent', 'opened', 'clicked'])->count();
        $clicked = $this->emailLogs()->where('click_count', '>', 0)->count();

        return $sent > 0 ? round(($clicked / $sent) * 100, 2) : 0;
    }

    /**
     * Check if campaign is active.
     */
    public function isActive()
    {
        return $this->status === 'active' && $this->is_active;
    }

    /**
     * Check if campaign can be started.
     */
    public function canBeStarted()
    {
        return $this->status === 'draft' && $this->workflows()->count() > 0;
    }

    /**
     * Start the campaign.
     */
    public function start()
    {
        if ($this->canBeStarted()) {
            $this->update([
                'status' => 'active',
                'is_active' => true,
                'started_at' => now(),
            ]);
        }
    }

    /**
     * Pause the campaign.
     */
    public function pause()
    {
        if ($this->isActive()) {
            $this->update([
                'status' => 'paused',
                'is_active' => false,
            ]);
        }
    }

    /**
     * Resume the campaign.
     */
    public function resume()
    {
        if ($this->status === 'paused') {
            $this->update([
                'status' => 'active',
                'is_active' => true,
            ]);
        }
    }

    /**
     * Complete the campaign.
     */
    public function complete()
    {
        $this->update([
            'status' => 'completed',
            'is_active' => false,
            'completed_at' => now(),
        ]);
    }

    /**
     * Get participants for this campaign.
     */
    public function getParticipants()
    {
        if ($this->webinar_id) {
            return WebinarParticipant::where('webinar_id', $this->webinar_id)->get();
        }

        // If no specific webinar, get participants from all user's webinars
        $webinarIds = $this->user->webinars()->pluck('id');
        return WebinarParticipant::whereIn('webinar_id', $webinarIds)->get();
    }

    /**
     * Check if participant should receive emails from this campaign.
     */
    public function shouldSendToParticipant(WebinarParticipant $participant)
    {
        // Basic check - can be extended with more complex conditions
        if ($this->webinar_id && $participant->webinar_id !== $this->webinar_id) {
            return false;
        }

        // Check if participant already received emails from this campaign
        $existingLog = $this->emailLogs()
            ->where('webinar_participant_id', $participant->id)
            ->exists();

        return !$existingLog;
    }

    /**
     * Get next workflow to execute for a participant.
     */
    public function getNextWorkflowForParticipant(WebinarParticipant $participant)
    {
        $executedWorkflows = $this->emailLogs()
            ->where('webinar_participant_id', $participant->id)
            ->pluck('workflow_id');

        return $this->workflows()
            ->whereNotIn('id', $executedWorkflows)
            ->where('is_active', true)
            ->orderBy('sequence_order')
            ->first();
    }
}
