<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\EmailLog;
use Illuminate\Support\Facades\Log;

class EmailTrackingController extends Controller
{
    /**
     * Track email open (tracking pixel)
     */
    public function trackOpen(Request $request, $logId)
    {
        try {
            $emailLog = EmailLog::find($logId);

            if ($emailLog && $emailLog->status === 'sent') {
                $emailLog->markAsOpened();

                Log::info("Email opened", [
                    'email_log_id' => $logId,
                    'recipient_email' => $emailLog->recipient_email,
                    'campaign_id' => $emailLog->campaign_id,
                    'user_agent' => $request->userAgent(),
                    'ip' => $request->ip()
                ]);
            }

            // Return 1x1 transparent pixel
            $pixel = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

            return response($pixel)
                ->header('Content-Type', 'image/gif')
                ->header('Content-Length', strlen($pixel))
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            Log::error("Email tracking error: " . $e->getMessage());

            // Still return pixel even on error
            $pixel = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
            return response($pixel)->header('Content-Type', 'image/gif');
        }
    }

    /**
     * Track email click
     */
    public function trackClick(Request $request, $logId)
    {
        try {
            $emailLog = EmailLog::find($logId);
            $originalUrl = base64_decode($request->get('url', ''));

            if ($emailLog) {
                $emailLog->markAsClicked();

                Log::info("Email link clicked", [
                    'email_log_id' => $logId,
                    'recipient_email' => $emailLog->recipient_email,
                    'campaign_id' => $emailLog->campaign_id,
                    'clicked_url' => $originalUrl,
                    'user_agent' => $request->userAgent(),
                    'ip' => $request->ip()
                ]);
            }

            // Redirect to original URL
            if ($originalUrl && filter_var($originalUrl, FILTER_VALIDATE_URL)) {
                return redirect($originalUrl);
            } else {
                return redirect('/');
            }

        } catch (\Exception $e) {
            Log::error("Email click tracking error: " . $e->getMessage());

            // Redirect to home on error
            return redirect('/');
        }
    }

    /**
     * Unsubscribe from email marketing
     */
    public function unsubscribe(Request $request, $participantId)
    {
        try {
            $participant = \App\Models\WebinarParticipant::find($participantId);

            if ($participant) {
                // Mark participant as unsubscribed (you might want to add this field)
                // For now, we'll just log it
                Log::info("Unsubscribe request", [
                    'participant_id' => $participantId,
                    'email' => $participant->email,
                    'ip' => $request->ip()
                ]);

                return view('email-marketing.unsubscribe-success', compact('participant'));
            }

            return view('email-marketing.unsubscribe-error');

        } catch (\Exception $e) {
            Log::error("Unsubscribe error: " . $e->getMessage());
            return view('email-marketing.unsubscribe-error');
        }
    }
}
