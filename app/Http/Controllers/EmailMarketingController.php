<?php

namespace App\Http\Controllers;

use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\EmailWorkflow;
use App\Models\EmailLog;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class EmailMarketingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->can('email marketing')) {
                abort(403, 'Bạn không có quyền truy cập tính năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display email marketing dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Get statistics
        $totalCampaigns = EmailCampaign::where('user_id', $user->id)->count();
        $activeCampaigns = EmailCampaign::where('user_id', $user->id)
                                      ->where('status', 'active')
                                      ->count();

        $totalEmailsSent = EmailLog::whereHas('campaign', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('status', 'sent')->count();

        $totalEmailsOpened = EmailLog::whereHas('campaign', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('status', 'opened')->count();

        // Calculate open rate
        $openRate = $totalEmailsSent > 0 ? round(($totalEmailsOpened / $totalEmailsSent) * 100, 2) : 0;

        // Get recent campaigns
        $recentCampaigns = EmailCampaign::where('user_id', $user->id)
            ->with(['webinar', 'emailLogs'])
            ->latest()
            ->take(5)
            ->get();

        // Get recent email logs
        $recentLogs = EmailLog::whereHas('campaign', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->with(['campaign', 'participant'])
            ->latest()
            ->take(10)
            ->get();

        return view('email-marketing.index', compact(
            'totalCampaigns',
            'activeCampaigns',
            'totalEmailsSent',
            'totalEmailsOpened',
            'openRate',
            'recentCampaigns',
            'recentLogs'
        ));
    }

    /**
     * Display campaigns list.
     */
    public function campaigns(Request $request)
    {
        $user = Auth::user();
        $query = EmailCampaign::where('user_id', $user->id)
            ->with(['webinar', 'workflows', 'emailLogs']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('webinar_id')) {
            $query->where('webinar_id', $request->webinar_id);
        }

        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $campaigns = $query->latest()->paginate(15);

        return view('email-marketing.campaigns.index', compact('campaigns'));
    }

    /**
     * Show the form for creating a new campaign.
     */
    public function createCampaign()
    {
        $user = Auth::user();
        $webinars = Webinar::where('user_id', $user->id)->get();

        return view('email-marketing.campaigns.create', compact('webinars'));
    }

    /**
     * Store a newly created campaign.
     */
    public function storeCampaign(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'webinar_id' => 'required|exists:webinars,id',
            'status' => 'required|in:draft,active,paused,completed',
        ]);

        $user = Auth::user();

        // Verify webinar belongs to user
        $webinar = Webinar::where('id', $request->webinar_id)
                         ->where('user_id', $user->id)
                         ->firstOrFail();

        $campaign = EmailCampaign::create([
            'user_id' => $user->id,
            'webinar_id' => $request->webinar_id,
            'name' => $request->name,
            'description' => $request->description,
            'trigger_type' => 'manual', // Đặt mặc định là manual
            'status' => $request->status,
            'is_active' => $request->status === 'active',
            'started_at' => $request->status === 'active' ? now() : null,
        ]);

        Alert::success('Thành công', 'Chiến dịch đã được tạo thành công!');

        return redirect()->route('email-marketing.workflows', $campaign);
    }

    /**
     * Display the specified campaign.
     */
    public function showCampaign(EmailCampaign $campaign)
    {
        if ($campaign->user_id !== Auth::id()) {
            abort(403);
        }

        $campaign->load(['webinar', 'workflows.emailTemplate', 'emailLogs']);

        return view('email-marketing.campaigns.show', compact('campaign'));
    }

    /**
     * Show the form for editing the specified campaign.
     */
    public function editCampaign(EmailCampaign $campaign)
    {
        if ($campaign->user_id !== Auth::id()) {
            abort(403);
        }

        $user = Auth::user();
        $webinars = Webinar::where('user_id', $user->id)->get();

        return view('email-marketing.campaigns.edit', compact('campaign', 'webinars'));
    }

    /**
     * Update the specified campaign.
     */
    public function updateCampaign(Request $request, EmailCampaign $campaign)
    {
        if ($campaign->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'webinar_id' => 'required|exists:webinars,id',
            'status' => 'required|in:draft,active,paused,completed',
        ]);

        $user = Auth::user();

        // Verify webinar belongs to user
        $webinar = Webinar::where('id', $request->webinar_id)
                         ->where('user_id', $user->id)
                         ->firstOrFail();

        $campaign->update([
            'webinar_id' => $request->webinar_id,
            'name' => $request->name,
            'description' => $request->description,
            'status' => $request->status,
            'is_active' => $request->status === 'active',
            'started_at' => $request->status === 'active' && !$campaign->started_at ? now() : $campaign->started_at,
            'completed_at' => $request->status === 'completed' ? now() : null,
        ]);

        Alert::success('Thành công', 'Chiến dịch đã được cập nhật!');

        return redirect()->route('email-marketing.campaigns.show', $campaign);
    }

    /**
     * Remove the specified campaign.
     */
    public function destroyCampaign(EmailCampaign $campaign)
    {
        if ($campaign->user_id !== Auth::id()) {
            abort(403);
        }

        $campaign->delete();

        Alert::success('Thành công', 'Chiến dịch đã được xóa!');

        return redirect()->route('email-marketing.campaigns');
    }

    /**
     * Display workflows for a campaign.
     */
    public function workflows(EmailCampaign $campaign)
    {
        if ($campaign->user_id !== Auth::id()) {
            abort(403);
        }

        $campaign->load(['workflows.emailTemplate', 'webinar']);

        return view('email-marketing.workflows.index', compact('campaign'));
    }

    /**
     * Store a new workflow.
     */
    public function storeWorkflow(Request $request, EmailCampaign $campaign)
    {
        if ($campaign->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'sequence_order' => 'required|integer|min:1',
            'scheduled_date' => 'required|date|after_or_equal:today',
            'scheduled_time' => 'required|date_format:H:i',
            'email_subject' => 'required|string|max:255',
            'email_content' => 'required|string',
        ]);

        // Create email template first
        $template = EmailTemplate::create([
            'user_id' => Auth::id(),
            'campaign_id' => $campaign->id,
            'name' => 'Template for ' . $campaign->name . ' - Step ' . $request->sequence_order,
            'subject' => $request->email_subject,
            'content' => $request->email_content,
            'template_type' => 'custom',
        ]);

        // Combine date and time to create scheduled datetime
        $scheduledDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $request->scheduled_date . ' ' . $request->scheduled_time);

        // Create workflow
        $workflow = EmailWorkflow::create([
            'campaign_id' => $campaign->id,
            'email_template_id' => $template->id,
            'sequence_order' => $request->sequence_order,
            'delay_days' => 0,
            'delay_hours' => 0,
            'delay_minutes' => 0,
            'scheduled_at' => $scheduledDateTime,
            'is_active' => true,
        ]);

        Alert::success('Thành công', 'Workflow đã được thêm!');

        return redirect()->route('email-marketing.workflows', $campaign);
    }

    /**
     * Display email logs.
     */
    public function logs(Request $request)
    {
        $user = Auth::user();
        $query = EmailLog::whereHas('campaign', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->with(['campaign', 'workflow', 'participant']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('campaign_id')) {
            $query->where('campaign_id', $request->campaign_id);
        }

        $logs = $query->latest()->paginate(20);
        $campaigns = EmailCampaign::where('user_id', $user->id)->get();

        return view('email-marketing.logs', compact('logs', 'campaigns'));
    }

    /**
     * Display analytics.
     */
    public function analytics()
    {
        $user = Auth::user();

        // Get detailed analytics data
        $campaigns = EmailCampaign::where('user_id', $user->id)
            ->with(['emailLogs'])
            ->get();

        return view('email-marketing.analytics', compact('campaigns'));
    }

    /**
     * Show workflow details (API endpoint).
     */
    public function showWorkflow(EmailWorkflow $workflow)
    {
        // Check if user owns this workflow's campaign
        if ($workflow->campaign->user_id !== Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $workflow->load(['emailTemplate', 'campaign']);

        $stats = [
            'total_sent' => $workflow->emailLogs()->whereIn('status', ['sent', 'opened', 'clicked'])->count(),
            'total_opened' => $workflow->emailLogs()->where('open_count', '>', 0)->count(),
            'total_clicked' => $workflow->emailLogs()->where('click_count', '>', 0)->count(),
            'total_failed' => $workflow->emailLogs()->where('status', 'failed')->count(),
        ];

        return response()->json([
            'id' => $workflow->id,
            'sequence_order' => $workflow->sequence_order,
            'is_active' => $workflow->is_active,
            'scheduled_at' => $workflow->scheduled_at,
            'email_template' => $workflow->emailTemplate,
            'stats' => $stats,
        ]);
    }

    /**
     * Update a workflow.
     */
    public function updateWorkflow(Request $request, EmailWorkflow $workflow)
    {
        // Check if user owns this workflow's campaign
        if ($workflow->campaign->user_id !== Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $rules = [];

        // If updating just status
        if ($request->has('is_active') && count($request->all()) == 1) {
            $rules['is_active'] = 'required|boolean';
        } else {
            // Full update
            $rules = [
                'sequence_order' => 'required|integer|min:1',
                'scheduled_date' => 'required|date|after_or_equal:today',
                'scheduled_time' => 'required|date_format:H:i',
                'email_subject' => 'required|string|max:255',
                'email_content' => 'required|string',
                'is_active' => 'sometimes|boolean',
            ];
        }

        $request->validate($rules);

        try {
            // Update workflow
            $updateData = [];

            if ($request->has('is_active')) {
                $updateData['is_active'] = $request->is_active;
            }

            if ($request->has('sequence_order')) {
                $updateData['sequence_order'] = $request->sequence_order;
            }

            if ($request->has('scheduled_date') && $request->has('scheduled_time')) {
                $scheduledDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i',
                    $request->scheduled_date . ' ' . $request->scheduled_time);
                $updateData['scheduled_at'] = $scheduledDateTime;
            }

            $workflow->update($updateData);

            // Update email template if provided
            if ($request->has('email_subject') && $request->has('email_content')) {
                $workflow->emailTemplate->update([
                    'subject' => $request->email_subject,
                    'content' => $request->email_content,
                ]);
            }

            // If scheduled time was updated, handle email logs
            if ($request->has('scheduled_date') && $request->has('scheduled_time')) {
                $this->handleWorkflowScheduleUpdate($workflow);
            }

            return response()->json(['success' => true, 'message' => 'Workflow đã được cập nhật']);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Handle workflow schedule update - recreate email logs for new schedule
     */
    protected function handleWorkflowScheduleUpdate(EmailWorkflow $workflow)
    {
        try {
            // Delete pending email logs for this workflow (not sent yet)
            EmailLog::where('workflow_id', $workflow->id)
                ->where('status', 'pending')
                ->delete();

            // Get participants for this workflow's campaign
            $participants = WebinarParticipant::where('webinar_id', $workflow->campaign->webinar_id)
                ->where('is_student', false)
                ->get();

            // Create new email logs for the updated schedule
            foreach ($participants as $participant) {
                // Check if there's already a sent email for this participant
                $sentEmail = EmailLog::where('workflow_id', $workflow->id)
                    ->where('webinar_participant_id', $participant->id)
                    ->where('status', '!=', 'pending')
                    ->first();

                // Only create new log if no email was sent yet
                if (!$sentEmail) {
                    EmailLog::create([
                        'campaign_id' => $workflow->campaign_id,
                        'workflow_id' => $workflow->id,
                        'webinar_participant_id' => $participant->id,
                        'recipient_email' => $participant->email,
                        'recipient_name' => $participant->name,
                        'subject' => $workflow->emailTemplate->subject,
                        'content' => $workflow->emailTemplate->content,
                        'status' => 'pending',
                        'scheduled_at' => $workflow->scheduled_at,
                        'open_count' => 0,
                        'click_count' => 0,
                    ]);
                }
            }

            \Log::info("Recreated email logs for workflow {$workflow->id} with new schedule: {$workflow->scheduled_at}");

        } catch (\Exception $e) {
            \Log::error("Error handling workflow schedule update: " . $e->getMessage());
        }
    }

    /**
     * Delete a workflow.
     */
    public function destroyWorkflow(EmailWorkflow $workflow)
    {
        // Check if user owns this workflow's campaign
        if ($workflow->campaign->user_id !== Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        try {
            // Delete related email logs first
            $workflow->emailLogs()->delete();

            // Delete email template
            if ($workflow->emailTemplate) {
                $workflow->emailTemplate->delete();
            }

            // Delete workflow
            $workflow->delete();

            return response()->json(['success' => true, 'message' => 'Workflow đã được xóa']);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Send test email for a workflow.
     */
    public function sendTestEmail(Request $request, EmailWorkflow $workflow)
    {
        // Check if user owns this workflow's campaign
        if ($workflow->campaign->user_id !== Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'test_email' => 'required|email',
            'test_name' => 'nullable|string|max:255',
        ]);

        try {
            $template = $workflow->emailTemplate;
            $campaign = $workflow->campaign;
            $webinar = $campaign->webinar;

            // Prepare test variables
            $variables = [
                'name' => $request->test_name ?: 'Người dùng thử nghiệm',
                'email' => $request->test_email,
                'phone' => '0123456789',
                'webinar_title' => $webinar ? $webinar->title : 'Webinar Demo',
                'webinar_speaker' => $webinar ? $webinar->speaker : 'Diễn giả Demo',
                'join_url' => $webinar ? route('join.show', $webinar->join_code) : '#',
                'join_code' => $webinar ? $webinar->join_code : 'DEMO123',
            ];

            // Replace variables in template
            $processedTemplate = $template->replaceVariables($variables);

            // Send email using Laravel's Mail facade
            \Mail::send([], [], function ($message) use ($request, $processedTemplate) {
                $message->to($request->test_email)
                        ->subject('[TEST] ' . $processedTemplate['subject'])
                        ->html($processedTemplate['content']);
            });

            return response()->json([
                'success' => true,
                'message' => 'Email thử đã được gửi thành công đến ' . $request->test_email
            ]);

        } catch (\Exception $e) {
            \Log::error('Test email failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send system test email to check SMTP configuration.
     */
    public function sendSystemTestEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            $testEmail = $request->test_email;
            $userName = Auth::user()->name;

            // Send simple test email
            \Mail::send([], [], function ($message) use ($testEmail, $userName) {
                $message->to($testEmail)
                        ->subject('[TEST] Kiểm tra hệ thống Email Marketing')
                        ->html("
                            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
                                <h2 style='color: #2563eb; text-align: center;'>🎉 Test Email Thành Công!</h2>
                                <div style='background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                                    <p><strong>Xin chào {$userName},</strong></p>
                                    <p>Đây là email test từ hệ thống Email Marketing của bạn.</p>
                                    <p>Nếu bạn nhận được email này, có nghĩa là cấu hình SMTP đang hoạt động tốt!</p>
                                </div>
                                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;'>
                                    <p style='color: #6b7280; font-size: 14px;'>
                                        Email được gửi lúc: " . now()->format('d/m/Y H:i:s') . "
                                    </p>
                                    <p style='color: #6b7280; font-size: 14px;'>
                                        Từ hệ thống Email Marketing
                                    </p>
                                </div>
                            </div>
                        ");
            });

            return response()->json([
                'success' => true,
                'message' => 'Email test đã được gửi thành công đến ' . $testEmail . '. Vui lòng kiểm tra hộp thư của bạn!'
            ]);

        } catch (\Exception $e) {
            \Log::error('System test email failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi email: ' . $e->getMessage()
            ], 500);
        }
    }
}
