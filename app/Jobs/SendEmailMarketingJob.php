<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Models\EmailLog;

class SendEmailMarketingJob implements ShouldQueue
{
    use Queueable;

    protected $emailLog;

    /**
     * Create a new job instance.
     */
    public function __construct(EmailLog $emailLog)
    {
        $this->emailLog = $emailLog;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get email log with relationships
            $emailLog = $this->emailLog->load(['workflow.emailTemplate', 'participant', 'campaign.webinar']);

            // Check if email should still be sent
            if ($emailLog->status !== 'pending') {
                Log::info("Email log {$emailLog->id} is not pending, skipping");
                return;
            }

            // Check if scheduled time has passed
            if ($emailLog->scheduled_at && $emailLog->scheduled_at->isFuture()) {
                Log::info("Email log {$emailLog->id} is scheduled for future, skipping");
                return;
            }

            // Prepare email data
            $template = $emailLog->workflow->emailTemplate;
            $participant = $emailLog->participant;
            $webinar = $emailLog->campaign->webinar;

            // Prepare variables for template
            $variables = $this->prepareEmailVariables($participant, $webinar);

            // Render email content
            $rendered = $template->renderContent($variables);

            // Add tracking pixel and process links
            $trackedContent = $this->addEmailTracking($rendered['content'], $emailLog);

            // Send email using Laravel Mail
            Mail::send([], [], function ($message) use ($rendered, $emailLog, $trackedContent) {
                $message->to($emailLog->recipient_email, $emailLog->recipient_name)
                        ->subject($rendered['subject'])
                        ->html($trackedContent);

                // Set from address from settings
                $fromEmail = \App\Models\Setting::get('smtp_from_address', config('mail.from.address'));
                $fromName = \App\Models\Setting::get('smtp_from_name', config('mail.from.name'));
                $message->from($fromEmail, $fromName);
            });

            // Mark as sent
            $emailLog->markAsSent();

            Log::info("Email sent successfully to {$emailLog->recipient_email}");

        } catch (\Exception $e) {
            // Mark as failed
            $this->emailLog->markAsFailed($e->getMessage());

            Log::error("Failed to send email to {$this->emailLog->recipient_email}: " . $e->getMessage());

            // Re-throw exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Prepare variables for email template.
     */
    private function prepareEmailVariables($participant, $webinar)
    {
        $variables = [
            '{name}' => $participant->name ?? '',
            '{email}' => $participant->email ?? '',
            '{phone}' => $participant->phone ?? '',
        ];

        if ($webinar) {
            $variables['{webinar_title}'] = $webinar->title ?? '';
            $variables['{webinar_description}'] = $webinar->description ?? '';
            $variables['{speaker}'] = $webinar->speaker ?? '';
            $variables['{webinar_date}'] = $webinar->scheduled_at ? $webinar->scheduled_at->format('d/m/Y H:i') : '';
            $variables['{join_url}'] = route('join.show', $webinar->join_code);
            $variables['{join_code}'] = $webinar->join_code ?? '';
        }

        // Add registration date
        if ($participant->created_at) {
            $variables['{registration_date}'] = $participant->created_at->format('d/m/Y H:i');
        }

        return $variables;
    }

    /**
     * Add tracking pixel and convert links to tracking links
     */
    private function addEmailTracking($content, $emailLog)
    {
        // Add tracking pixel at the end of email
        $trackingPixelUrl = route('email-marketing.track.open', ['log' => $emailLog->id]);
        $trackingPixel = '<img src="' . $trackingPixelUrl . '" width="1" height="1" style="display:none;" alt="">';

        // Convert all links to tracking links
        $content = preg_replace_callback(
            '/<a\s+(?:[^>]*?\s+)?href=(["\'])(.*?)\1/i',
            function ($matches) use ($emailLog) {
                $originalUrl = $matches[2];

                // Skip if it's already a tracking URL or email/tel links
                if (strpos($originalUrl, 'email-tracking') !== false ||
                    strpos($originalUrl, 'mailto:') === 0 ||
                    strpos($originalUrl, 'tel:') === 0 ||
                    strpos($originalUrl, '#') === 0) {
                    return $matches[0];
                }

                // Create tracking URL
                $trackingUrl = route('email-marketing.track.click', [
                    'log' => $emailLog->id,
                    'url' => base64_encode($originalUrl)
                ]);

                return str_replace($matches[2], $trackingUrl, $matches[0]);
            },
            $content
        );

        // Add tracking pixel before closing body tag, or at the end if no body tag
        if (strpos($content, '</body>') !== false) {
            $content = str_replace('</body>', $trackingPixel . '</body>', $content);
        } else {
            $content .= $trackingPixel;
        }

        return $content;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $this->emailLog->markAsFailed($exception->getMessage());
        Log::error("Email job failed permanently for log {$this->emailLog->id}: " . $exception->getMessage());
    }
}
